{"name": "🌰 MuchSkills MASTER", "lastModified": "2025-08-08T10:31:34Z", "thumbnailUrl": "https://s3-alpha.figma.com/thumbnails/2bcb0d28-61c4-419e-af3a-db3f86a6a5ef?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCS2LHWM5N%2F20250807%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250807T000000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=8b014d3c71c12f99beacb4e666d3f0e9fce7e2d9d9caed33c32a82e674ceb27c", "version": "2249864700090641152", "role": "viewer", "editorType": "figma", "linkAccess": "inherit", "nodes": {"23540:137532": {"document": {"id": "23540:137532", "name": "Frame 117419", "type": "FRAME", "scrollBehavior": "SCROLLS", "children": [{"id": "23540:137311", "name": "Connect your MuchSkills account to CV Inventory for a seamless skills-based CV creation experience. If you do not have one, you can sign up here to and get started.", "type": "TEXT", "scrollBehavior": "SCROLLS", "blendMode": "PASS_THROUGH", "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.****************, "g": 0.****************, "b": 0.****************, "a": 1}}], "strokes": [], "strokeWeight": 1, "strokeAlign": "OUTSIDE", "styles": {"fill": "1:182", "text": "1:108"}, "absoluteBoundingBox": {"x": 12556, "y": -17849, "width": 586, "height": 32}, "absoluteRenderBounds": {"x": 12556.5703125, "y": -17844.*********, "width": 580.**********, "height": 27.********}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "characters": "Connect your MuchSkills account to CV Inventory for a seamless skills-based CV creation experience. If you do not have one, you can sign up here to and get started.", "characterStyleOverrides": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2], "styleOverrideTable": {"2": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "isOverrideOverTextStyle": true, "semanticWeight": "BOLD", "fontSize": 12, "letterSpacing": 0, "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.*****************, "g": 0.**************, "b": 0.********65348816, "a": 1}}], "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%", "inheritFillStyleId": "1:233", "inheritTextStyleId": "1:108"}, "3": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Bold", "fontStyle": "Bold", "fontWeight": 700, "textAutoResize": "WIDTH_AND_HEIGHT", "isOverrideOverTextStyle": true, "semanticWeight": "BOLD", "fontSize": 12, "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%", "inheritTextStyleId": "1:108"}}, "lineTypes": ["NONE"], "lineIndentations": [0], "style": {"fontFamily": "<PERSON><PERSON>", "fontPostScriptName": "Lato-Regular", "fontStyle": "Regular", "fontWeight": 400, "textAutoResize": "HEIGHT", "fontSize": 12, "textAlignHorizontal": "LEFT", "textAlignVertical": "CENTER", "letterSpacing": 0, "lineHeightPx": 15.59999942779541, "lineHeightPercent": 108.33332824707031, "lineHeightPercentFontSize": 130, "lineHeightUnit": "FONT_SIZE_%"}, "layoutVersion": 4, "effects": [], "interactions": []}], "blendMode": "PASS_THROUGH", "clipsContent": false, "background": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9490196108818054, "g": 0.9490196108818054, "b": 0.9490196108818054, "a": 1}}], "fills": [{"blendMode": "NORMAL", "type": "SOLID", "color": {"r": 0.9490196108818054, "g": 0.9490196108818054, "b": 0.9490196108818054, "a": 1}}], "strokes": [], "cornerRadius": 4, "cornerSmoothing": 0, "strokeWeight": 1, "strokeAlign": "INSIDE", "backgroundColor": {"r": 0.9490196108818054, "g": 0.9490196108818054, "b": 0.9490196108818054, "a": 1}, "styles": {"fills": "1:107", "fill": "1:107"}, "layoutMode": "HORIZONTAL", "primaryAxisSizingMode": "FIXED", "counterAxisAlignItems": "CENTER", "primaryAxisAlignItems": "CENTER", "paddingLeft": 8, "paddingRight": 8, "paddingTop": 8, "paddingBottom": 8, "itemSpacing": 8, "layoutWrap": "NO_WRAP", "absoluteBoundingBox": {"x": 12548, "y": -17857, "width": 602, "height": 48}, "absoluteRenderBounds": {"x": 12548, "y": -17857, "width": 602, "height": 48}, "constraints": {"vertical": "TOP", "horizontal": "LEFT"}, "layoutAlign": "INHERIT", "layoutGrow": 1, "layoutSizingHorizontal": "FILL", "layoutSizingVertical": "HUG", "effects": [], "interactions": []}, "components": {}, "componentSets": {}, "schemaVersion": 0, "styles": {"1:182": {"key": "637b23cdeeacc4b559915e52c173ace9f64cf358", "name": "msGray-3", "styleType": "FILL", "remote": true, "description": ""}, "1:108": {"key": "dea7c4e1f1f1a63c1581d0da078a8b1b45ecd414", "name": "Small Doge/smalldoge-4R", "styleType": "TEXT", "remote": true, "description": ""}, "1:233": {"key": "1e63eb31e7bb771ea039e722ea9e2be548b4bc0e", "name": "msBlue-1", "styleType": "FILL", "remote": true, "description": ""}, "1:107": {"key": "b3dffc5274d58c14d370acb1506f21005ecef60e", "name": "msGray-6", "styleType": "FILL", "remote": true, "description": ""}}}}}