{"name": "@cvinventory/source", "version": "0.0.0", "license": "MIT", "scripts": {"dev": "npx nx run-many --target=serve --projects=web,api --parallel --output-style=stream", "build:web": "nx build web --prod", "build:api": "nx build api --prod", "build": "yarn build:web && yarn build:api", "start": "node dist/apps/api/main.js", "prepare": "husky install", "lint": "yarn eslint . --fix", "migrate:create": "sh -c 'ts-node --project apps/api/tsconfig.app.json node_modules/.bin/migrate-mongo-ts create $0 -f apps/api/migrate-mongo-config.ts'", "migrate:status": "ts-node --project apps/api/tsconfig.app.json node_modules/.bin/migrate-mongo-ts status -f apps/api/migrate-mongo-config.ts", "migrate:up": "ts-node --project apps/api/tsconfig.app.json node_modules/.bin/migrate-mongo-ts up -f apps/api/migrate-mongo-config.ts", "migrate:down": "ts-node --project apps/api/tsconfig.app.json node_modules/.bin/migrate-mongo-ts down -f apps/api/migrate-mongo-config.ts"}, "private": true, "dependencies": {"@casl/ability": "^6.7.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^5.0.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.2", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.2", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/platform-express": "^10.0.2", "@nestjs/schedule": "^6.0.0", "@nestjs/throttler": "^6.4.0", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-pdf/renderer": "^4.1.6", "@sendgrid/mail": "^8.1.5", "@tanstack/react-query": "^5.64.2", "@tanstack/react-query-devtools": "^5.64.2", "@types/express": "^5.0.3", "@types/lodash": "^4.17.15", "@types/multer": "^1.4.13", "@types/stripe": "^8.0.417", "@types/uniqid": "^5.3.4", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "helmet": "^8.1.0", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lucide-react": "^0.475.0", "migrate-mongo-ts": "^1.1.5", "mongoose": "^8.13.2", "nestjs-pino": "^4.4.0", "next-themes": "^0.4.6", "nodemailer": "^7.0.2", "pino-http": "^10.4.0", "react": "18.3.1", "react-day-picker": "8.10.1", "react-dom": "18.3.1", "react-hook-form": "^7.56.3", "react-medium-image-zoom": "^5.2.14", "react-pdf": "^10.0.1", "react-pdf-html": "^2.1.3", "react-quill": "^2.0.0", "react-router-dom": "^7.1.3", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "sonner": "^2.0.3", "stripe": "^18.1.1", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "ts-node": "10.9.1", "typescript": "5.7.3", "uniqid": "^5.4.0", "use-debounce": "^10.0.4", "zod": "^3.24.4"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/eslint": "21.1.2", "@nx/eslint-plugin": "21.1.2", "@nx/jest": "21.1.2", "@nx/js": "21.1.2", "@nx/nest": "21.1.2", "@nx/node": "21.1.2", "@nx/react": "21.1.2", "@nx/vite": "21.1.2", "@nx/web": "21.1.2", "@nx/webpack": "21.1.2", "@nx/workspace": "21.1.2", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.3.12", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "15.0.6", "@types/cookie-parser": "^1.4.8", "@types/jest": "^29.5.12", "@types/node": "^22.13.5", "@types/nodemailer": "^6.4.17", "@types/react": "18.3.1", "@types/react-dom": "^19.0.3", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.2.0", "@vitest/ui": "^1.3.1", "autoprefixer": "10.4.13", "babel-jest": "^29.7.0", "eslint": "^9.8.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "lint-staged": "^16.0.0", "nestjs-spelunker": "^1.3.2", "nx": "21.1.2", "pino-pretty": "^13.0.0", "postcss": "8.4.38", "prettier": "^3.5.3", "tailwindcss": "3.4.3", "ts-jest": "^29.1.0", "tslib": "^2.3.0", "typescript-eslint": "^8.13.0", "vite": "6.3.5", "vite-plugin-svgr": "^4.3.0", "vitest": "^1.3.1", "webpack-cli": "^5.1.4"}, "resolutions": {"string-width": "^4.2.3", "wrap-ansi": "^7.0.0", "strip-ansi": "^6.0.1"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["eslint --fix"]}}