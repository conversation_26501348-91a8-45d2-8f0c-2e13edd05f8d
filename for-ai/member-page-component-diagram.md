```mermaid
graph TD
    subgraph Page Components
        A1[CvList]
        A2[CvForm]
        A3[CvSettingsForm]
        A4[CvPreview]
        A5[MemberBreadcrumb]
    end

    subgraph Common UI Components
        B1[ButtonPrimary]
        B2[ButtonSecondary]
        B3[Drawer]
    end

    subgraph External Libraries
        C1[lucide-react icons <br/>(ChevronLeft, Plus)]
    end

    MemberPage --> A1
    MemberPage --> A2
    MemberPage --> A3
    MemberPage --> A4
    MemberPage --> A5
    MemberPage --> B1
    MemberPage --> B2
    MemberPage --> B3
    MemberPage --> C1

    classDef page fill:#3498db,stroke:#2980b9,stroke-width:2px,color:#fff;
    class MemberPage page;
```
