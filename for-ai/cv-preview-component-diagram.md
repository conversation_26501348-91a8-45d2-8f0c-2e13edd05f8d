```mermaid
graph TD
    subgraph External Libraries
        A1[PDFDownloadLink]
        A2[lucide-react icons <br/>(ChevronDown, FileDown, Pencil, Settings)]
    end

    subgraph Templates
        B1[EuropassTemplate]
        B2[CleanTemplate]
        B3[ProfessionalTemplate]
    end

    subgraph Common Components
        C1[ButtonSecondary]
        C2[DropdownMenu]
        C3[PDFViewer]
        C4[AiChat]
    end

    CvPreview --> A1
    CvPreview --> A2
    CvPreview --> B1
    CvPreview --> B2
    CvPreview --> B3
    CvPreview --> C1
    CvPreview --> C2
    CvPreview --> C3
    CvPreview --> C4

    classDef component fill:#2ecc71,stroke:#27ae60,stroke-width:2px,color:#fff;
    class CvPreview component;
```

### Component Breakdown:

**`CvPreview`** is the central component responsible for displaying a complete CV preview. It orchestrates data fetching, state management, and the rendering of various UI elements.

#### External Libraries:
-   **`@react-pdf/renderer`**:
    -   `PDFDownloadLink`: Used to generate and provide a download link for the CV in PDF format.
-   **`lucide-react`**:
    -   Provides icons like `ChevronDown`, `FileDown`, `Pencil`, and `Settings` for buttons and interactive elements.

#### CV Templates:
These components are responsible for the visual layout of the CV data. The `CvPreview` component dynamically selects one of these based on user settings.
-   **`EuropassTemplate`**
-   **`CleanTemplate`**
-   **`ProfessionalTemplate`**

#### Common & Feature Components:
These are reusable or feature-specific components from within the application.
-   **`ButtonSecondary`**: A standard secondary button used for actions like "Edit CV".
-   **`DropdownMenu`**: A composite component (including `DropdownMenuTrigger`, `DropdownMenuContent`, etc.) used for creating dropdowns for selecting the CV status and template.
-   **`PDFViewer`**: Renders the selected CV template within the browser for previewing.
-   **`AiChat`**: A component that provides an AI-powered chat interface to interact with and modify the CV content.
