# Multi-stage Dockerfile for CV Builder Monorepo
# Optimized for production deployment with minimal image size

# Build stage - Using a more robust base image for Nx
FROM node:22 AS builder

ARG VITE_API_BASE_URL
ARG VITE_WEB_BASE_URL
ARG CACHE_BUST

# Set environment variables to avoid issues
ENV NODE_OPTIONS="--max-old-space-size=4096"
ENV NX_SKIP_NX_CACHE=true
ENV HUSKY_SKIP_INSTALL=1
ENV CI=true

ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_WEB_BASE_URL=${VITE_WEB_BASE_URL}

WORKDIR /app

# Cache bust for dependencies when package files change
RUN echo "Cache bust: ${CACHE_BUST:-default}"

# Copy package files
COPY package.json yarn.lock ./

# Configure yarn to use npm registry and install dependencies
RUN yarn config set npmRegistryServer https://registry.npmjs.org && \
    yarn config set network-timeout 300000 && \
    yarn config set retry-count 3 && \
    echo "Using registry: $(yarn config get npmRegistryServer)" && \
    yarn install --frozen-lockfile --ignore-scripts

# Copy application code (filtered by .dockerignore)
COPY . .

RUN yarn build

# Production stage - Use a smaller image for runtime
FROM node:22-slim AS production

# Install curl for health check and build tools for native modules
RUN apt-get update && apt-get install -y curl python3 make g++ && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy package.json and yarn.lock first (for better layer caching)
COPY package.json yarn.lock ./

# Install only production dependencies first
RUN yarn config set npmRegistryServer https://registry.npmjs.org && \
    yarn config set network-timeout 300000 && \
    yarn config set retry-count 3 && \
    echo "Using registry: $(yarn config get npmRegistryServer)" && \
    yarn install --frozen-lockfile --production && \
    yarn cache clean

# Copy built files from builder - using correct Nx output paths
COPY --from=builder /app/dist/apps/web ./dist/apps/web
COPY --from=builder /app/dist/apps/api ./dist/apps/api

# Create app user for security
RUN groupadd -g 1001 nodejs && \
    useradd -r -u 1001 -g nodejs appuser

# Create necessary directories
RUN mkdir -p /app/uploads /app/logs && \
    chown -R appuser:nodejs /app/uploads /app/logs

# Switch to non-root user
USER appuser

# Expose application port
EXPOSE 3000

# Set Node.js to run in production mode
ENV NODE_ENV=production

# Health check
# HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
#    CMD curl -f http://localhost:3000/health || exit 1

# Start the application
### Not working part, commented out for now
# COPY --chown=appuser:nodejs docker-entrypoint.sh .
# RUN chmod +x docker-entrypoint.sh
# ENTRYPOINT ["./docker-entrypoint.sh"]
CMD ["yarn", "start"]
