import {
  <PERSON><PERSON><PERSON>cy,
  CvStatus,
  MemberSource,
  TimeRange,
  UserType,
  UserData,
  Template,
  ExpertiseLevelEnum,
} from './types';

export interface CostRateInput {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

export interface EducationRecordInput {
  schoolName: string;
  degree?: string;
  description?: string;
  startDate?: string | Date;
  endDate?: string | Date;
}

export interface WorkHistoryRecordInput {
  companyName: string;
  roleTitle?: string;
  description?: string;
  startDate: string | Date;
  endDate?: string | Date;
  isCurrent?: boolean;
}

export interface CreateMemberInput extends UpdateMemberInput {
  source: MemberSource;
}

export interface MemberSkillInput {
  msId: string;
  name: string;
  level: ExpertiseLevelEnum;
  isSoftware: boolean;
}

export interface MemberCertificationInput {
  msId: string;
  name: string;
  organization: string;
}

export interface UpdateMemberInput {
  avatar?: string;
  avatarFile?: undefined;
  avatarPreview?: undefined;
  firstName: string;
  lastName?: string;
  email?: string;
  location?: string;
  telephone?: string;
  currentPosition?: string;
  currentLevel?: string;
  yearsOfExperience?: number;
  languages?: string[];
  type?: UserType;
  clients?: string[];
  costRate?: CostRateInput;
  costToCompany?: CostRateInput;
  socials?: string[];
  workExperience?: WorkHistoryRecordInput[];
  education?: EducationRecordInput[];
  certifications?: MemberCertificationInput[];
  skills?: MemberSkillInput[];
}

export interface CvPreferencesInput {
  title: string;
  maxPages?: number;
  role?: string;
  level?: string;
  costRate?: CostRateInput;
  customer?: string;
  link?: string;
  contractStart?: Date;
  contractEnd?: Date;
  autoRenewal?: boolean;
  leastExperience?: number;
  maxExperience?: number;
  skills?: string[];
  description?: string;
}

export interface UpdateCvInput {
  preferences?: CvPreferencesInput;
  status?: CvStatus;
  template?: Template;
}

export interface CvSectionBase {
  order: number;
  title: string;
  active: boolean;
}

export interface HidableInputInput {
  value: string;
  active: boolean;
}

export interface EducationDataItemInput extends Partial<EducationRecordInput> {
  active?: boolean;
}

export interface WorkHistoryDataItemInput
  extends Partial<WorkHistoryRecordInput> {
  active?: boolean;
}

export interface PersonalInfoInput extends CvSectionBase {
  data: {
    firstName: HidableInputInput;
    lastName: HidableInputInput;
    jobTitle: HidableInputInput;
    location: HidableInputInput;
    nationality: HidableInputInput;
    email: HidableInputInput;
    telephone: HidableInputInput;
    socials: { inputs: string[]; active: boolean };
  };
}

export interface AboutMeInput extends CvSectionBase {
  data: { description: string };
}

export interface WorkHistoryInput extends CvSectionBase {
  data: WorkHistoryDataItemInput[];
}

export interface EducationInput extends CvSectionBase {
  data: EducationDataItemInput[];
}

export interface CertificationsInput extends CvSectionBase {
  data: { description: string };
}

export interface SkillsInput extends CvSectionBase {
  data: { description: string };
}

export interface LanguagesInput extends CvSectionBase {
  data: {
    languages: string[];
  };
}

export interface CustomInput extends CvSectionBase {
  data: { description: string };
}

export interface UpdateCvSectionsInput {
  personalInfo: PersonalInfoInput;
  aboutMe: AboutMeInput;
  workHistory: WorkHistoryInput;
  education: EducationInput;
  certifications: CertificationsInput;
  skills: SkillsInput;
  languages: LanguagesInput;
  customSections: CustomInput[];
}

export interface UserInput extends Omit<UserData, '_id' | 'email' | 'avatar'> {
  avatar?: string | null | File;
}
