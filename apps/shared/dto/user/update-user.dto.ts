import { IsOptional, IsString, IsEnum, IsBoolean } from 'class-validator';

import { UserRole } from '../../types';

export { DeleteProfileDto } from './delete-profile.dto';

export class UpdateUserDto {
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  // Assuming avatar can be a string (URL) or null to remove it.
  // File uploads are typically handled differently (e.g., multipart/form-data)
  // and might involve a separate endpoint or a more complex DTO structure.
  // For now, let's assume it's a URL or null.
  @IsOptional()
  @IsString()
  avatar?: string | null;

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  department?: string;

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean; // If you need to deactivate/activate users
}
