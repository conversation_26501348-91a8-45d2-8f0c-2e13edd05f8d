import { Type } from 'class-transformer';
import {
  IsArray,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { MessageRole } from 'shared/types';
import { Trim } from 'shared/utils/decorators';

export class MessageDto {
  @IsString()
  @IsNotEmpty()
  role: MessageRole;

  @Trim()
  @IsString()
  @IsNotEmpty()
  content: string;
}

export class RegenerateCvDto {
  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Query cannot be empty' })
  query: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MessageDto)
  messages?: MessageDto[];
}
