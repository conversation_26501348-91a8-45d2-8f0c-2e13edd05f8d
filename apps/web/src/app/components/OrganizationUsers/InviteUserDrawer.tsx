import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { ChevronDown } from 'lucide-react';
import { useMemo, useState } from 'react';
import { UserRole } from 'shared/types';
import { toast } from 'sonner';

import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Drawer } from '@/components/common/Drawer';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/common/DropdownMenu';
import { Input } from '@/components/common/Input';
import { TypographyLabel } from '@/components/Settings/Typography/Typography';
import { createOrganizationInviteRequest } from '@/helpers/requests';

const roleOptions = [
  { value: UserRole.ADMIN, label: 'Admin' },
  { value: UserRole.MEMBER, label: 'Member' },
];

interface InviteUserDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function InviteUserDrawer({ isOpen, onClose }: InviteUserDrawerProps) {
  const queryClient = useQueryClient();
  const [inviteEmail, setInviteEmail] = useState('');
  const [newInviteRole, setNewInviteRole] = useState<UserRole>(UserRole.MEMBER);
  const [error, setError] = useState('');

  const renderField = (
    label: string,
    children: React.ReactNode,
    error?: string,
  ) => (
    <div className="grid grid-cols-[200px_1fr] items-center gap-x-4 py-2">
      <div className="font-bold text-smalldoge-3 text-msGray-3">{label}</div>
      <div>
        {children}
        {error && <p className="mt-1 text-smalldoge-2 text-msError">{error}</p>}
      </div>
    </div>
  );

  const { mutate: createInvite, isPending: isCreatingInvite } = useMutation({
    mutationFn: createOrganizationInviteRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['organizationInvites'] });
      setInviteEmail('');
      setNewInviteRole(UserRole.MEMBER);
      setError('');
      onClose();
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      const errorMessage = message || 'Failed to send invite';
      setError(errorMessage);
      toast.error(errorMessage);
    },
  });

  const selectedRoleLabel = useMemo(() => {
    return (
      roleOptions.find((option) => option.value === newInviteRole)?.label ||
      newInviteRole
    );
  }, [newInviteRole]);

  const handleSendInvite = () => {
    if (!inviteEmail) return;
    setError(''); // Clear any previous errors
    createInvite({
      email: inviteEmail,
      role: newInviteRole,
    });
  };

  const handleClose = () => {
    setInviteEmail('');
    setNewInviteRole(UserRole.MEMBER);
    setError('');
    onClose();
  };

  return (
    <Drawer active={isOpen} onClose={handleClose} title="Invite new member">
      <div className="space-y-4">
        {renderField(
          'Email',
          <Input
            id="email"
            type="email"
            value={inviteEmail}
            onChange={(e) => setInviteEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full font-bold text-smalldoge-3 text-msBlack"
          />,
        )}
        {renderField(
          'Role',
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center ml-1 cursor-pointer bg-background text-foreground">
                <TypographyLabel className="text-smalldoge-3">
                  {selectedRoleLabel}
                </TypographyLabel>
                <ChevronDown className="w-4 h-4 ml-1" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="start"
              className="w-fit prevent-drawer-outside-click"
            >
              <DropdownMenuGroup>
                {roleOptions.map((option) => (
                  <DropdownMenuItem
                    key={option.value}
                    onClick={() => setNewInviteRole(option.value)}
                    className="font-bold text-smalldoge-3"
                  >
                    <TypographyLabel className="text-smalldoge-3">
                      {option.label}
                    </TypographyLabel>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>,
        )}
        {error && (
          <div className="p-3 border rounded-md bg-msRed-3 border-msRed-2">
            <p className="text-smalldoge-3 text-msRed-1">{error}</p>
          </div>
        )}
        <div className="flex justify-end pt-4">
          <ButtonPrimary
            onClick={handleSendInvite}
            disabled={isCreatingInvite || !inviteEmail}
            className="px-2 py-1"
            variant="blackCompact"
          >
            {isCreatingInvite ? 'Sending Invite...' : 'Send Invite'}
          </ButtonPrimary>
        </div>
      </div>
    </Drawer>
  );
}
