import { useEffect } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';

import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';

const AuthRoute = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    // If user is authenticated, redirect to home
    if (!loading && user) {
      navigate(NAVIGATE_PATH.home, { replace: true });
    }
  }, [navigate, user, loading]);

  // Don't render anything while loading
  if (loading) {
    return null;
  }

  return <Outlet />;
};

export default AuthRoute;
