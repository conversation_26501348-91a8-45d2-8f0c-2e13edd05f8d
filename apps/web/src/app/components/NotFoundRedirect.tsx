import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';

const NotFoundRedirect = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (user) {
        navigate(NAVIGATE_PATH.home, { replace: true });
      } else {
        navigate(NAVIGATE_PATH.login, { replace: true });
      }
    }
  }, [navigate, user, loading]);

  return null;
};

export default NotFoundRedirect;
