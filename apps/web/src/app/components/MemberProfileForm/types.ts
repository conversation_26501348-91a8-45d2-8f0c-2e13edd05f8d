import {
  Currency,
  ExpertiseLevelEnum,
  TimeRange,
  UserType,
} from 'shared/types';
import { z } from 'zod';

export const costRateSchema = z.object({
  currency: z.nativeEnum(Currency),
  amount: z.number(),
  timeRange: z.nativeEnum(TimeRange),
});

export const skillSchema = z.object({
  msId: z.string(),
  name: z.string(),
  level: z.nativeEnum(ExpertiseLevelEnum),
  isSoftware: z.boolean(),
});

export const certificationSchema = z.object({
  msId: z.string(),
  name: z.string(),
  organization: z.string(),
});

export const educationRecordSchema = z.object({
  schoolName: z.string().min(1, 'School name is required'),
  degree: z.string().optional(),
  description: z.string().optional(),
  startDate: z.union([z.string(), z.date()]).optional(),
  endDate: z.union([z.string(), z.date()]).optional(),
});

export const workHistoryRecordSchema = z.object({
  companyName: z.string().min(1, 'Company name is required'),
  roleTitle: z.string().optional(),
  description: z.string().optional(),
  startDate: z.union([z.string(), z.date()]).refine((val) => !!val, {
    message: 'Start date is required',
  }),
  endDate: z.union([z.string(), z.date()]).optional(),
  isCurrent: z.boolean().optional(),
});

export const memberProfileSchema = z.object({
  avatarFile: z.any().optional().nullable(),
  avatarPreview: z.string().optional().nullable(),
  firstName: z
    .string()
    .min(3, 'First name must be at least 3 characters long')
    .regex(/^[a-zA-Z]+$/, 'First name must not contain symbols'),
  lastName: z.string().optional(),
  email: z
    .string()
    .optional()
    .refine(
      (val) => !val || val === '' || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val),
      { message: 'Invalid email address' },
    ),
  location: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val) {
          return true;
        }
        return /.+, .+/.test(val);
      },
      {
        message: 'Location should be in "City, Country" format',
      },
    ),
  telephone: z
    .string()
    .optional()
    .refine((val) => !val || /^[+\d\s().-]{7,20}$/.test(val), {
      message: 'Invalid phone number',
    }),
  currentPosition: z.string().optional(),
  currentLevel: z.string().optional(),
  type: z.nativeEnum(UserType).optional(),
  clients: z.array(z.string()),
  costRate: costRateSchema,
  costToCompany: costRateSchema,
  yearsOfExperience: z.number().optional(),
  socials: z.array(z.string()),
  languages: z.array(z.string()),
  education: z.array(educationRecordSchema),
  workExperience: z.array(workHistoryRecordSchema),
  skills: z.array(skillSchema),
  certifications: z.array(certificationSchema),
});

export type MemberProfileFormValues = z.infer<typeof memberProfileSchema>;
export type EducationItemType = MemberProfileFormValues['education'][number];
export type WorkHistoryItemType =
  MemberProfileFormValues['workExperience'][number];
