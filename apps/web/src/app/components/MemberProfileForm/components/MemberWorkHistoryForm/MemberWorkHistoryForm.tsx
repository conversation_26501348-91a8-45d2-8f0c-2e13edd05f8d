import { useFormContext, useFieldArray } from 'react-hook-form';

import { WorkHistoryItem } from './components';
import { ButtonSecondary } from '../../../common';
import { MemberProfileFormValues } from '../../types';

interface MemberWorkHistoryFormProps {
  readonly?: boolean;
  externalMember?: boolean;
}

export const MemberWorkHistoryForm = ({
  readonly,
  externalMember,
}: MemberWorkHistoryFormProps) => {
  const { control } = useFormContext<MemberProfileFormValues>();
  const { fields, append, remove } = useFieldArray<
    MemberProfileFormValues,
    'workExperience'
  >({
    control,
    name: 'workExperience',
  });

  return (
    <div>
      {fields.length === 0 ? (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/notes.svg" alt="notes" />
          <span className="text-smalldoge-3 text-center">
            {externalMember ? (
              <>
                No work experience records found from your HR system.
                <br />
                You can make changes there and they will be reflected here after
                sync.
              </>
            ) : (
              <>There are no work experience records yet.</>
            )}
          </span>
          {!readonly && (
            <ButtonSecondary
              disabled={readonly}
              onClick={() => append({ companyName: '', startDate: new Date() })}
            >
              Add new record
            </ButtonSecondary>
          )}
        </div>
      ) : (
        <>
          <div className="flex flex-col space-y-5 p-px">
            {fields.map((field, i) => (
              <WorkHistoryItem
                key={field.id}
                index={i}
                dropDownItems={[
                  {
                    title: 'Remove',
                    onClick: () => remove(i),
                  },
                ]}
                readonly={readonly}
              />
            ))}
          </div>
          {!readonly && (
            <ButtonSecondary
              type="button"
              className="mt-4"
              onClick={() => append({ companyName: '', startDate: new Date() })}
            >
              Add new record
            </ButtonSecondary>
          )}
        </>
      )}
    </div>
  );
};
