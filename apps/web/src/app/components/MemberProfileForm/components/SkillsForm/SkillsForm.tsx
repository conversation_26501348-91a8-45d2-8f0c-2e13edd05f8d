import { Search } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import {
  expertiseLevelData,
  ExpertiseLevelEnum,
  MemberSkill,
  Skill,
} from 'shared/types';
import { useDebounce } from 'use-debounce';

import { MemberSkillTag, SkillsList } from './components';
import { MemberProfileFormValues } from '../../types';

import {
  Input,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/common';
import useOutsideClick from '@/hooks/useOutsideClick';

interface SkillsFormProps {
  orgId?: string;
  readonly?: boolean;
  externalMember?: boolean;
}

export function SkillsForm({
  orgId,
  readonly,
  externalMember,
}: SkillsFormProps) {
  const [popoverOpen, setPopoverOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [debounceSetSearchValue] = useDebounce(searchValue, 500);

  const popoverRef = useOutsideClick<HTMLDivElement>(handleOutsideClick);

  function handleOutsideClick(event: MouseEvent | TouchEvent) {
    const targetElement = event.target as HTMLElement;

    if (
      popoverOpen &&
      !targetElement.closest('.popover-content') &&
      !targetElement.classList.contains('popover-content')
    ) {
      setPopoverOpen(false);
    }
  }

  const { control, setValue } = useFormContext<MemberProfileFormValues>();
  const {
    fields: memberSkills,
    append,
    remove: removeMemberSkill,
  } = useFieldArray<MemberProfileFormValues, 'skills'>({
    control,
    name: 'skills',
  });

  const watchedMemberSkills = useWatch({
    control,
    name: 'skills',
  });

  const [techSkills, otherSkills] = useMemo(
    () =>
      watchedMemberSkills
        .sort((a, b) => {
          const levelDiff =
            expertiseLevelData[a.level].order -
            expertiseLevelData[b.level].order;
          if (levelDiff !== 0) return levelDiff;
          return a.name.localeCompare(b.name);
        })
        .reduce(
          (res: MemberSkill[][], memberSkill) => {
            if (memberSkill.isSoftware) {
              res[0].push(memberSkill);
            } else {
              res[1].push(memberSkill);
            }

            return res;
          },
          [[], []],
        ),
    [watchedMemberSkills],
  );

  return (
    <div className="flex flex-col min-h-0">
      {!readonly && (
        <div ref={popoverRef}>
          <Popover
            open={popoverOpen}
            onOpenChange={(val) => {
              if (popoverOpen) return;

              setPopoverOpen(val);
            }}
          >
            <PopoverTrigger asChild>
              <div className="mb-3">
                <Input
                  prefixElement={
                    <Search
                      className="absolute transform -translate-y-2.5 translate-x-2 top-[50%]"
                      size={20}
                    />
                  }
                  value={searchValue}
                  placeholder="Search a skill"
                  className="h-12 pl-8"
                  onChange={(e) => setSearchValue(e.target.value)}
                />
              </div>
            </PopoverTrigger>
            <PopoverContent
              className="p-0 prevent-drawer-outside-click popover-content"
              isFullWidth={true}
              align="start"
              sideOffset={10}
              collisionPadding={10}
              onOpenAutoFocus={(e) => e.preventDefault()}
            >
              <SkillsList
                searchValue={debounceSetSearchValue}
                skillsToExclude={memberSkills.map((ms) => ms.msId)}
                orgId={orgId}
                onAdd={(skill: Skill, level: ExpertiseLevelEnum) => {
                  append({
                    msId: skill.msId,
                    name: skill.name,
                    level,
                    isSoftware: skill.isSoftware,
                  });

                  setPopoverOpen(false);
                  setSearchValue('');
                }}
              />
            </PopoverContent>
          </Popover>
        </div>
      )}
      <div className="flex flex-col overflow-auto">
        {!!techSkills.length && (
          <div className="flex flex-col mb-4">
            <b className="text-smalldoge-4 text-msGray-3 uppercase mb-3">
              Technical Skills
            </b>
            <div className="flex flex-wrap">
              {techSkills.map((memberSkill) => {
                const msIndex = memberSkills.findIndex(
                  (ms) => ms.msId === memberSkill.msId,
                );

                return (
                  <MemberSkillTag
                    key={msIndex}
                    readonly={readonly}
                    memberSkill={memberSkill}
                    onRemove={() => removeMemberSkill(msIndex)}
                    onUpdate={(level) =>
                      setValue(`skills.${msIndex}.level`, level)
                    }
                  />
                );
              })}
            </div>
          </div>
        )}
        {!!otherSkills.length && (
          <div className="flex flex-col">
            <b className="text-smalldoge-4 text-msGray-3 uppercase mb-3">
              Other Skills
            </b>
            <div className="flex flex-wrap">
              {otherSkills.map((memberSkill) => {
                const msIndex = memberSkills.findIndex(
                  (ms) => ms.msId === memberSkill.msId,
                );

                return (
                  <MemberSkillTag
                    key={msIndex}
                    readonly={readonly}
                    memberSkill={memberSkill}
                    onRemove={() => removeMemberSkill(msIndex)}
                    onUpdate={(level) =>
                      setValue(`skills.${msIndex}.level`, level)
                    }
                  />
                );
              })}
            </div>
          </div>
        )}

        {!watchedMemberSkills.length && (
          <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
            <img src="/images/notes.svg" alt="notes" />
            <span className="text-smalldoge-3 text-center">
              {externalMember ? (
                <>
                  No skills found from your HR system.
                  <br />
                  You can make changes there and they will be reflected here
                  after sync.
                </>
              ) : (
                <>No skills added yet.</>
              )}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
