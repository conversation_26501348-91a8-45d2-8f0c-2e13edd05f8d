import { ExpertiseLevelEnum } from 'shared/types';

import { cn } from '@/lib/utils';

const levelButtons = [
  {
    id: ExpertiseLevelEnum.noLevel,
    name: 'No level',
    active: 'bg-msWhite text-msBlack border-msBlack',
    className:
      'hover:bg-msWhite hover:text-msBlack border border-msWhite hover:border-msBlack',
  },
  {
    id: ExpertiseLevelEnum.beginner,
    name: 'Beginner',
    active: 'bg-msYellow-1 text-msBlack',
    className: 'hover:bg-msYellow-1 hover:text-msBlack',
  },
  {
    id: ExpertiseLevelEnum.intermediate,
    name: 'Intermediate',
    active: 'bg-msGreen-2 text-msWhite',
    className: 'hover:bg-msGreen-2 hover:text-msWhite',
  },
  {
    id: ExpertiseLevelEnum.expert,
    name: 'Expert',
    active: 'bg-msBlack text-msWhite',
    className: 'hover:bg-msBlack hover:text-msWhite',
  },
];

interface ExpertisePickerProps {
  selectedLevel?: ExpertiseLevelEnum;
  onLevelClick: (level: ExpertiseLevelEnum) => void;
}

export function ExpertisePicker({
  selectedLevel,
  onLevelClick,
}: ExpertisePickerProps) {
  return (
    <div className="flex space-x-2 bg-msWhite rounded-lg p-2">
      {levelButtons.map((level) => (
        <button
          key={level.id}
          type="button"
          className={cn(
            'shrink-0 rounded-sm px-1 uppercase text-smalldoge-5 text-msGray-4 bg-msGray-6 transition-all',
            level.className,
            selectedLevel === level.id && level.active,
          )}
          onClick={() => onLevelClick(level.id)}
        >
          {level.name}
        </button>
      ))}
    </div>
  );
}
