import { X } from 'lucide-react';
import {
  expertiseLevelData,
  ExpertiseLevelEnum,
  MemberSkill,
} from 'shared/types';

import { ExpertisePicker } from '../components';

import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/common';
import { cn } from '@/lib/utils';

interface MemberSkillTagProps {
  readonly?: boolean;
  memberSkill: MemberSkill;
  onRemove: () => void;
  onUpdate: (level: ExpertiseLevelEnum) => void;
}

export function MemberSkillTag({
  readonly,
  memberSkill,
  onRemove,
  onUpdate,
}: MemberSkillTagProps) {
  return (
    <Tooltip>
      <TooltipTrigger type="button" className="mr-2 mb-2 cursor-default">
        <div className="h-6 w-fit flex items-center space-x-1 bg-msBlue-3 rounded-[100px] px-2 min-w-0">
          <span className="truncate text-smalldoge-3 text-msBlue-1">
            {memberSkill.name}
          </span>
          <span
            className={cn(
              'shrink-0 rounded-full size-3 border border-msBlack',
              expertiseLevelData[memberSkill.level].color,
            )}
          />
          {!readonly && (
            <button
              type="button"
              className="shrink-0"
              onClick={(e) => {
                e.stopPropagation();
                onRemove();
              }}
            >
              <X className="text-msBlue-1" size={12} strokeWidth={3} />
            </button>
          )}
        </div>
      </TooltipTrigger>
      {!readonly && (
        <TooltipContent
          sideOffset={5}
          className="p-0 shadow-md prevent-drawer-outside-click"
        >
          <ExpertisePicker
            selectedLevel={memberSkill.level}
            onLevelClick={onUpdate}
          />
        </TooltipContent>
      )}
    </Tooltip>
  );
}
