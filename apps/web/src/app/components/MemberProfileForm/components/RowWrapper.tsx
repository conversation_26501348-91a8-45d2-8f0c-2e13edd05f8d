interface RowWrapperProps {
  children: JSX.Element;
  label: string;
  required?: boolean;
}

export function RowWrapper({ children, label, required }: RowWrapperProps) {
  return (
    <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-start sm:space-x-1 sm:space-y-0">
      <div className="flex-shrink-0 w-40 font-bold text-smalldoge-4 text-msGray-3 sm:pt-2">
        {label}
        {required && <span className="text-msRed-1">*</span>}
      </div>
      {children}
    </div>
  );
}
