import { format } from 'date-fns';
import { CalendarIcon, Ellipsis } from 'lucide-react';
import { EducationDataItem } from 'shared/types';

import {
  Label,
  MonthPicker,
  Popover,
  PopoverTrigger,
  Button,
  PopoverContent,
  Checkbox,
} from '../../../../../../components';
import {
  Textarea,
  Input,
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  ButtonSecondary,
} from '../../../../../common';

import { cn } from '@/lib/utils';

interface EducationItemProps {
  data: EducationDataItem;
  dropDownItems: { title: string; onClick: () => void }[];
  readonly?: boolean;
  onChange: (updatedEducationRecord: EducationDataItem) => void;
}

export function EducationItem({
  data,
  dropDownItems,
  readonly,
  onChange,
}: EducationItemProps) {
  return (
    <div className="relative p-4 border rounded-sm">
      <div className="absolute flex space-x-2 right-1 top-1">
        <Checkbox
          checked={data.active}
          disabled={readonly}
          onCheckedChange={(val) => onChange({ ...data, active: !!val })}
        />
        <DropdownMenu modal={false}>
          <DropdownMenuTrigger asChild disabled={readonly}>
            <ButtonSecondary variant="icon">
              <Ellipsis size={14} />
            </ButtonSecondary>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            collisionPadding={10}
            align="end"
            className="w-32 prevent-drawer-outside-click"
          >
            <DropdownMenuGroup>
              {dropDownItems.map((item, i) => (
                <DropdownMenuItem key={i} onClick={item.onClick}>
                  <span className="text-smalldoge-3">{item.title}</span>
                </DropdownMenuItem>
              ))}
            </DropdownMenuGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      <div className="flex flex-col space-y-5">
        <div className="flex flex-col space-y-2">
          <Label label="School Name" labelProps={{ htmlFor: 'schoolName' }} />
          <Input
            id="schoolName"
            type="text"
            value={data.schoolName}
            disabled={readonly}
            onChange={(e) => onChange({ ...data, schoolName: e.target.value })}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label label="Degree" labelProps={{ htmlFor: 'degree' }} />
          <Input
            id="degree"
            type="text"
            value={data.degree}
            disabled={readonly}
            onChange={(e) => onChange({ ...data, degree: e.target.value })}
          />
        </div>
        <div className="flex flex-col space-y-2">
          <Label
            label="Description"
            labelProps={{ htmlFor: 'roleDescription' }}
          />
          <Textarea
            id="roleDescription"
            placeholder="Describe what you studied briefly"
            value={data.description}
            disabled={readonly}
            onChange={(e) => onChange({ ...data, description: e.target.value })}
            className="min-h-28"
          />
        </div>

        <div className="flex space-x-5">
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="Start" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data.startDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data.startDate ? (
                    format(data.startDate, 'MMM yyyy')
                  ) : (
                    <span>Pick start month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  maxDate={data.endDate}
                  selectedMonth={data.startDate}
                  onMonthSelect={(date) =>
                    onChange({ ...data, startDate: date })
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="flex flex-col w-1/2 space-y-2">
            <Label label="End" />
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={'outline'}
                  disabled={readonly}
                  className={cn(
                    'justify-start text-left font-normal',
                    !data.endDate && 'text-muted-foreground',
                  )}
                >
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  {data.endDate ? (
                    format(data.endDate, 'MMM yyyy')
                  ) : (
                    <span>Pick end month</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 prevent-drawer-outside-click">
                <MonthPicker
                  minDate={data.startDate}
                  selectedMonth={data.endDate}
                  onMonthSelect={(date) => onChange({ ...data, endDate: date })}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      </div>
    </div>
  );
}
