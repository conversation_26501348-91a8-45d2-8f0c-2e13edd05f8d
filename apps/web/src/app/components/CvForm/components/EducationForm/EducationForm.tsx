import { cloneDeep } from 'lodash';
import { memo } from 'react';
import { EducationDataItem, SectionData } from 'shared/types';

import { ButtonSecondary } from '../../../common';
import { EducationItem } from './components/EducationItem/EducationItem';

import { FormReducerAction, ReducerActionType } from '@/pages/MemberPage';

export const emptyEducationItem: EducationDataItem = {
  active: true,
  schoolName: '',
  degree: '',
  description: '',
  startDate: undefined,
  endDate: undefined,
};

interface EducationFormProps {
  sectionId: string;
  data: EducationDataItem[];
  readonly?: boolean;
  onDataChange: React.Dispatch<FormReducerAction>;
}

export const EducationForm = memo(
  ({ sectionId, data, readonly, onDataChange }: EducationFormProps) => {
    function updateHistoryRecord(
      index: number,
      updatedEducationRecord: EducationDataItem,
    ) {
      const tempData = cloneDeep(data);
      tempData[index] = updatedEducationRecord;

      handleChange(tempData);
    }

    function handleChange(data: SectionData) {
      onDataChange({
        type: ReducerActionType.updateField,
        sectionId: sectionId,
        data,
      });
    }

    function handleCreateRecord() {
      handleChange(data.concat(emptyEducationItem));
    }

    if (!data.length) {
      return (
        <div className="flex flex-col space-y-2 items-center justify-center border border-dashed rounded-[8px] px-2 py-6">
          <img src="/images/notes.svg" alt="notes" />
          <span className="text-smalldoge-3">
            There are no education records yet
          </span>
          <ButtonSecondary disabled={readonly} onClick={handleCreateRecord}>
            Add new record
          </ButtonSecondary>
        </div>
      );
    }

    return (
      <div>
        <div className="flex flex-col space-y-5 p-px">
          {data.map((educationRecord, i) => (
            <EducationItem
              key={i}
              data={educationRecord}
              dropDownItems={[
                {
                  title: 'Remove',
                  onClick: () => {
                    const clonedData = cloneDeep(data);
                    clonedData.splice(i, 1);

                    handleChange(clonedData);
                  },
                },
              ]}
              readonly={readonly}
              onChange={(val) => updateHistoryRecord(i, val)}
            />
          ))}
        </div>
        <ButtonSecondary
          disabled={readonly}
          className="mt-4"
          onClick={handleCreateRecord}
        >
          Add new record
        </ButtonSecondary>
      </div>
    );
  },
);
