import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { MuchskillsMember, OrganizationFullInfo } from 'shared/types';
import { toast } from 'sonner';

import { Avatar, ButtonSecondary, Loader } from '@/components/common';
import { createMemberFromMuchskills } from '@/helpers/requests';
import { cn } from '@/lib/utils';

interface MembersListProps {
  orgId: string;
  membersFetching: boolean;
  noMembersFound: boolean;
  members?: MuchskillsMember[];
  onMemberAdded: () => void;
}

export function MembersList({
  orgId,
  membersFetching,
  noMembersFound,
  members,
  onMemberAdded,
}: MembersListProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { mutate: createMember } = useMutation({
    mutationFn: (email: string) => createMemberFromMuchskills({ orgId, email }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['paginatedMembers'] });
      onMemberAdded();

      // Update organization full info to increment totalMembers count
      queryClient.setQueryData(
        ['organizationFullInfo'],
        (oldData: OrganizationFullInfo | undefined) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            totalMembers: oldData.totalMembers + 1,
          };
        },
      );

      toast.success('Member added!');
    },
  });

  if (noMembersFound) {
    return (
      <div className="custom-dashed-border flex flex-col items-center space-y-2 justify-center rounded-[8px] px-2 py-6">
        <img
          width={246}
          src="/images/guys-with-magnifier.svg"
          alt="no matches"
        />
        <span className="text-smalldoge-3">Unable to find any candidates.</span>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-3 sm:grid-cols-3">
      {members?.map((member) => (
        <div
          key={member.email}
          className="relative flex flex-col items-center p-3 border rounded-sm border-msGray-5 group"
        >
          <div className="absolute top-0 left-0 flex items-center justify-center w-full h-full transition-all rounded-sm opacity-0 group-hover:opacity-100 bg-msBlue-3 bg-opacity-60">
            {member.profileExist ? (
              <ButtonSecondary
                onClick={() => navigate(`/member/${member.localId}`)}
              >
                Create CV
              </ButtonSecondary>
            ) : (
              <ButtonSecondary onClick={() => createMember(member.email)}>
                Add member
              </ButtonSecondary>
            )}
          </div>
          {member.profileExist ? (
            <div
              className={cn(
                'w-full flex justify-between bg-msYellow-2 rounded-[8px] px-2 mb-2',
                member.cvsCount && 'bg-msBlue-3',
              )}
            >
              <span className="text-smalldoge-4">CVs created</span>
              <b className="text-smalldoge-4">{member.cvsCount}</b>
            </div>
          ) : (
            <div className="w-full flex justify-between bg-msGray-5 rounded-[8px] px-2 mb-2">
              <span className="text-smalldoge-4">Not added</span>
            </div>
          )}

          <div className="flex items-center mb-1 space-x-3">
            <span className="flex items-center justify-center w-6 h-6 rounded-full bg-msBlack">
              <b className="text-smalldoge-4 text-msWhite">
                {member.competencesCount}
              </b>
            </span>
            <Avatar url={member.image} />
            <img
              className="w-6 h-6"
              src="/icons/TreeGreen.svg"
              alt="freshness"
            />
          </div>

          <b className="mb-2 text-center text-smalldoge-3 line-clamp-2">
            {member.name}
          </b>

          <span className="mb-2 text-center text-smalldoge-4 text-msGray-3 line-clamp-2">
            {member.title || 'No job title set'}
          </span>

          {/* TODO: add flag displaying */}
          <span className="text-smalldoge-4 line-clamp-2">
            {member.location || '-'}
          </span>
        </div>
      ))}
    </div>
  );
}
