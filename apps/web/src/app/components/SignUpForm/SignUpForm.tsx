// FIXME('vladyslav'): Fix styles on this form(use shadcn title)
import { Mail } from 'lucide-react'; // Placeholder for Google Icon

import { Button } from '@/components/common/Button'; // Using general Button for Google
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Input } from '@/components/common/Input';

export function SignUpForm(): React.JSX.Element {
  return (
    <div className="flex items-center justify-center min-h-screen p-4 bg-gray-100">
      <div className="w-full max-w-md p-10 bg-white rounded-lg shadow-xl md:p-20">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-gray-900">
            Create an account
          </h1>
          <p className="text-sm text-gray-500">
            Enter your email below to create your account
          </p>
        </div>

        <div className="space-y-2">
          <div>
            {/* According to Figma, there's no explicit label, just placeholder */}
            <Input
              type="email"
              placeholder="<EMAIL>"
              className="w-full"
              // wrapperClassName="mb-4" // Figma shows input directly followed by button
            />
          </div>
          <ButtonPrimary className="w-full rounded-md">
            Sign in with email
          </ButtonPrimary>
        </div>

        <div className="flex items-center my-6">
          <div className="flex-grow border-t border-gray-300"></div>
          <span className="mx-4 text-xs font-normal text-gray-400 uppercase">
            OR CONTINUE WITH
          </span>
          <div className="flex-grow border-t border-gray-300"></div>
        </div>

        <Button
          variant="outline"
          className="flex items-center justify-center w-full"
        >
          <Mail className="w-4 h-4 mr-2" /> {/* Placeholder Google Icon */}
          Google
        </Button>

        <p className="px-6 mt-8 text-xs text-center text-gray-400">
          By clicking continue, you agree to our{' '}
          {/* TODO: add Terms of Service and Privacy Policy */}
          {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
          <a href="#" className="underline hover:text-gray-600">
            Terms of Service
          </a>{' '}
          and {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
          <a href="#" className="underline hover:text-gray-600">
            Privacy Policy
          </a>
          .
        </p>
      </div>
    </div>
  );
}
