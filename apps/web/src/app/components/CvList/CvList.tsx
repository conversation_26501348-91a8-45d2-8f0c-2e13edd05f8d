import { FileUser } from 'lucide-react';
import { Cv } from 'shared/types';

import { CVListTab } from '../../components';
import { Loader } from '../common';

import { useDuplicateCv, useDeleteCv } from '@/hooks/useCvMutations';

interface CvListProps {
  memberId: string;
  previewingCv?: string;
  memberCvs?: Cv[];
  isCvsLoading: boolean;
  onCvRemoved: () => void;
  onCvEdit: (cvId: string) => void;
  onCvPreview: (cvId: string) => void;
  onNewCvClick: () => void;
}

export function CvList({
  memberId,
  previewingCv,
  memberCvs,
  isCvsLoading,
  onCvRemoved,
  onCvEdit,
  onCvPreview,
  onNewCvClick,
}: CvListProps) {
  const { mutate: duplicateCv } = useDuplicateCv();

  const { mutate: deleteCv } = useDeleteCv({
    onCvRemoved,
  });

  if (isCvsLoading) {
    return (
      <div className="flex justify-center w-full h-full py-20 overflow-auto">
        <Loader size={60} />
      </div>
    );
  }

  if (!memberCvs?.length) {
    return (
      <div className="flex flex-col h-full overflow-auto">
        <div className="flex flex-col h-full p-6">
          <span className="mb-2 font-bold text-smalldoge-4">
            Member has no CVs yet.
          </span>
          <div
            className="h-32 flex flex-col space-y-2 items-center justify-center border border-msGray-5 rounded-[8px] text-msGray-4 hover:border-msBlue-2 hover:text-msBlue-2 hover:bg-msBlue-4 transition-all cursor-pointer"
            onClick={onNewCvClick}
          >
            <FileUser size={32} />
            <span className="font-bold uppercase text-smalldoge-4">
              +New CV
            </span>
          </div>
          <img
            className="mt-auto"
            width={200}
            src="images/explorers-on-boat.svg"
            alt="placeholder"
          />
        </div>
      </div>
    );
  }
  return (
    <div className="flex flex-col h-full overflow-auto">
      {memberCvs.map((cv) => (
        <CVListTab
          key={cv._id}
          active={previewingCv === cv._id}
          cv={cv}
          onPreviewClick={() => onCvPreview(cv._id)}
          onEditClick={() => onCvEdit(cv._id)}
          onDelete={() => deleteCv({ cvId: cv._id, memberId })}
          onDuplicate={() => duplicateCv({ cvId: cv._id, memberId })}
        />
      ))}
    </div>
  );
}
