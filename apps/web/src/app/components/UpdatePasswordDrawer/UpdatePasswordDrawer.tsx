import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useForm, Controller } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { ButtonSecondary, Drawer, Input } from '@/components';
import { updatePasswordRequest } from '@/helpers/requests';

const updatePasswordSchema = z.object({
  oldPassword: z.string().min(1, 'Old password is required'),
  newPassword: z.string().min(8, 'New password must be at least 8 characters'),
});

type UpdatePasswordFormData = z.infer<typeof updatePasswordSchema>;

interface UpdatePasswordDrawerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function UpdatePasswordDrawer({
  isOpen,
  onClose,
}: UpdatePasswordDrawerProps) {
  const { mutate: updatePassword, isPending: isUpdating } = useMutation({
    mutationFn: updatePasswordRequest,
    onSuccess: () => {
      toast.success('Password updated successfully');
      reset();
      onClose();
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      toast.error(message || 'Failed to update password');
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { isDirty, errors },
  } = useForm<UpdatePasswordFormData>({
    resolver: zodResolver(updatePasswordSchema),
    defaultValues: {
      oldPassword: '',
      newPassword: '',
    },
  });

  const onSubmit = (data: UpdatePasswordFormData) => {
    updatePassword(data);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const renderField = (label: string, children: React.ReactNode) => (
    <div className="flex items-center gap-2">
      <div className="w-40 md:w-[200px]">
        <span className="font-bold text-smalldoge-3 text-msGray-3">
          {label}
        </span>
      </div>
      <div className="flex-1 py-1">{children}</div>
    </div>
  );

  return (
    <Drawer
      active={isOpen}
      onClose={handleClose}
      title="Update password"
      className="w-[680px]"
    >
      <div className="flex flex-col gap-6">
        <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8">
          <div className="flex flex-col gap-5">
            <div className="flex flex-col gap-2">
              {renderField(
                'Old password',
                <div className="p-1">
                  <Controller
                    name="oldPassword"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="password"
                        placeholder="Current Password"
                        className="font-bold text-smalldoge-3"
                        error={errors.oldPassword?.message}
                      />
                    )}
                  />
                </div>,
              )}

              {renderField(
                'New Password',
                <div className="p-1">
                  <Controller
                    name="newPassword"
                    control={control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        type="password"
                        placeholder="New Password"
                        className="font-bold bg-transparent text-smalldoge-3"
                        error={errors.newPassword?.message}
                      />
                    )}
                  />
                </div>,
              )}
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <ButtonSecondary
              variant="ghost"
              text="uppercase"
              onClick={handleClose}
              disabled={isUpdating}
              type="button"
            >
              Cancel
            </ButtonSecondary>
            <ButtonSecondary
              type="submit"
              text="uppercase"
              disabled={isUpdating || !isDirty}
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </ButtonSecondary>
          </div>
        </form>
      </div>
    </Drawer>
  );
}
