import { pdf } from '@react-pdf/renderer';
import { ZoomIn, ZoomOut } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';

import { Loader, Pagination, Shimmer } from '../common';

import { cn } from '@/lib/utils';

// Set the PDF worker
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.mjs',
  import.meta.url,
).toString();

interface PDFViewerProps {
  template: JSX.Element;
  isAIGenerationPending?: boolean;
}

export const PDFViewer = ({
  template,
  isAIGenerationPending = false,
}: PDFViewerProps) => {
  const [initialLoading, setInitialLoading] = useState<boolean>(true);
  const [pdfRendering, setPdfRendering] = useState<boolean>(false);
  const [currentBlob, setCurrentBlob] = useState<Blob>();
  const [dummyBlob, setDummyBlob] = useState<Blob>();
  const [pageWidth, setPageWidth] = useState<number>(600);
  const [scale, setScale] = useState<number>(1);
  const [page, setPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);

  const pdfContainer = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const updateWidth = () => {
      if (!pdfContainer.current) return;

      const contentWidth = pdfContainer.current.offsetWidth;
      setPageWidth(contentWidth);
    };

    updateWidth();

    window.addEventListener('resize', updateWidth);

    return () => {
      window.addEventListener('resize', updateWidth);
    };
  }, []);

  useEffect(() => {
    const generatePdf = async () => {
      const pdfBlob = await pdf(template).toBlob();

      setCurrentBlob(pdfBlob);
      setPdfRendering(true);
      if (!dummyBlob) setDummyBlob(pdfBlob);
    };

    generatePdf();
  }, [template]);

  return (
    <div className="flex flex-col space-y-1">
      <div className="flex justify-between mb-1">
        <Pagination
          page={page}
          itemsPerPage={1}
          total={totalPages}
          minimalMode={true}
          onPageChange={setPage}
        />
        <div className="flex space-x-1">
          <b className="text-msGray-3">{Number(scale * 100).toFixed()}%</b>
          <button
            className={cn(scale <= 1 && 'text-msGray-5')}
            disabled={scale <= 1}
            onClick={() => setScale((val) => val - 0.1)}
          >
            <ZoomOut size={20} />
          </button>
          <button
            className={cn(scale >= 2 && 'text-msGray-5')}
            disabled={scale >= 2}
            onClick={() => setScale((val) => val + 0.1)}
          >
            <ZoomIn size={20} />
          </button>
        </div>
      </div>
      <div className="w-full border border-msGray-4 rounded-sm p-0.5 aspect-[210/296] overflow-auto">
        <Shimmer isActive={isAIGenerationPending}>
          <div ref={pdfContainer} className="relative w-full h-full">
            {/* Placeholder document */}
            <Document file={dummyBlob}>
              <Page
                pageNumber={page}
                renderTextLayer={false}
                renderAnnotationLayer={false}
                width={pageWidth}
                scale={scale}
              />
            </Document>
            <Document
              className={cn(
                'absolute top-0 left-0 w-full h-full',
                pdfRendering && 'opacity-0',
              )}
              file={currentBlob}
              onLoadSuccess={({ numPages }) => setTotalPages(numPages)}
            >
              <Page
                pageNumber={page}
                renderTextLayer={false}
                renderAnnotationLayer={false}
                width={pageWidth}
                scale={scale}
                onRenderSuccess={() => {
                  //onRenderSuccess triggered sooner than page actually rendered
                  //setTimeout imitate the moment the page is rendered
                  setTimeout(() => {
                    setPdfRendering(false);
                    setDummyBlob(currentBlob);
                  }, 100);

                  setTimeout(() => {
                    setInitialLoading(false);
                  }, 500);
                }}
              />
            </Document>
            {initialLoading && (
              <div className="absolute top-0 left-0 w-full h-full bg-msWhite flex items-center justify-center">
                <Loader size={40} />
              </div>
            )}
          </div>
        </Shimmer>
      </div>
    </div>
  );
};
