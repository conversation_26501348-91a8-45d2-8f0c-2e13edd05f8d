// Probably we don't need this component since we started using Lucide
import classNames from 'classnames';

import {
  close,
  doubleArrow,
  down,
  gear,
  move,
  plusSign,
  projDraft,
  sectorChart,
  table,
  team,
} from './icons';

const BUNDLED_ICONS = {
  close,
  doubleArrow,
  down,
  gear,
  move,
  plusSign,
  projDraft,
  sectorChart,
  table,
  team,
};

export type BundledIcon = keyof typeof BUNDLED_ICONS;

interface IconProps {
  source: BundledIcon;
  size: 16 | 20 | 24 | 32 | 40;
  fillColor?: string;
}

export function Icon({ source, size, fillColor }: IconProps) {
  const IconComponent = BUNDLED_ICONS[source];

  return (
    <IconComponent
      className={classNames('flex-shrink-0', fillColor && `fill-${fillColor}`)}
      width={size}
      height={size}
    />
  );
}
