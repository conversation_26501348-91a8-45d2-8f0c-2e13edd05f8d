import { ArrowDownWideNarrow, ChevronDown } from 'lucide-react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/common/DropdownMenu';
import { cn } from '@/lib/utils';

export interface SortOption {
  label: string;
  value: string;
}

interface SortDropdownProps {
  sortOptions: SortOption[];
  selectedSort: SortOption;
  onSortChange: (option: SortOption) => void;
  align?: 'start' | 'center' | 'end';
  className?: string;
}

export function SortDropdown({
  sortOptions,
  selectedSort,
  onSortChange,
  align = 'end',
  className = '',
}: SortDropdownProps) {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {/* Desktop: Show "Sort by" text */}
      <span className="hidden md:inline text-smalldoge-3 text-msGray-3">
        Sort by
      </span>
      {/* Mobile: Show sort icon */}
      <ArrowDownWideNarrow size={16} className="md:hidden text-msGray-3" />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <div className="flex items-center gap-1 cursor-pointer">
            {/* Desktop: Show full label */}
            <span className="hidden font-bold transition-colors duration-100 md:inline text-smalldoge-3 hover:text-msGray-3">
              {selectedSort.label}
            </span>
            {/* Mobile: Show simplified text */}
            <span className="font-normal md:hidden text-smalldoge-3">
              {selectedSort.label}
            </span>
            <ChevronDown size={16} />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={align} className="w-40">
          <DropdownMenuGroup>
            {sortOptions.map((option) => (
              <DropdownMenuItem
                key={option.value}
                onClick={() => onSortChange(option)}
              >
                <span className="text-smalldoge-3">{option.label}</span>
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
