import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const buttonPrimaryVariants = cva(
  'h-10 w-fit px-7 rounded-[100px] text-smalldoge-1 font-bold transition-all transform active:scale-95 disabled:opacity-50 disabled:pointer-events-none disabled:shadow-[0px_4px_10px_0px_rgba(0,0,0,0)]',
  {
    variants: {
      variant: {
        black: 'bg-msBlack text-msWhite hover:bg-msGray-2 active:bg-msBlack',
        white:
          'bg-msWhite text-msBlack border border-msBlack border-[2px] hover:bg-msGray-6 active:bg-msGray-5',
        blackCompact:
          'h-fit bg-msBlack text-msWhite py-1 px-2 rounded-[2px] text-smalldoge-4 hover:bg-msGray-2 active:bg-msBlack',
        link: 'h-fit p-0 rounded-[2px] text-smalldoge-3 text-msBlue-1 hover:text-msBlue-2 active:text-msGray-5',
      },
      shadow: {
        shadow:
          'shadow-[0px_4px_10px_0px_rgba(0,0,0,0.15)] hover:shadow-[0px_10px_10px_0px_rgba(0,0,0,0.15)] active:shadow-[0px_4px_10px_0px_rgba(0,0,0,0)]',
      },
    },
    defaultVariants: {
      variant: 'black',
    },
  },
);

export interface ButtonPrimaryProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonPrimaryVariants> {
  asChild?: boolean;
}

const ButtonPrimary = React.forwardRef<HTMLButtonElement, ButtonPrimaryProps>(
  ({ className, variant, shadow, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(buttonPrimaryVariants({ variant, shadow, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
ButtonPrimary.displayName = 'ButtonPrimary';

export { ButtonPrimary, buttonPrimaryVariants };
