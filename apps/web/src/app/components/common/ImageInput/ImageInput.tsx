import { useRef, useState } from 'react';
import { MAX_AVATAR_SIZE } from 'shared/constants';
import { toast } from 'sonner';

import { Avatar } from '../Avatar';

import { cn } from '@/lib/utils';

interface ImageInputProps {
  url?: string | null;
  disabled?: boolean;
  isReadOnly?: boolean;
  error?: string;
  onImageUpdate: (file: File | null, previewUrl: string | null) => void;
}

export function ImageInput({
  url,
  disabled,
  isReadOnly,
  error,
  onImageUpdate,
}: ImageInputProps) {
  const [errorText, setErrorText] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleUploadFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setErrorText(
        'Please select a valid image file (JPG, JPEG, PNG, or WebP)',
      );
      toast.error('Please select a valid image file (JPG, JPEG, PNG, or WebP)');
      return;
    }

    if (file.size > MAX_AVATAR_SIZE) {
      setErrorText('Image must be smaller than 5MB');
      toast.error('Image must be smaller than 5MB');
      return;
    }

    // Create preview URL
    const reader = new FileReader();
    reader.onloadend = () => onImageUpdate(file, reader.result as string);
    reader.readAsDataURL(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  if (isReadOnly) {
    return (
      <div>
        <Avatar url={url} size={64} />
        {error && <p className="mt-1 text-smalldoge-2 text-msError">{error}</p>}
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center">
        <img
          src={url || '/images/person.png'}
          alt="preview"
          className="object-cover w-16 h-16 mr-1 rounded-full"
        />
        <span
          className={cn(
            'text-smalldoge-3 font-bold text-msGray-2 mr-2 cursor-pointer',
            disabled && 'pointer-events-none text-msGray-4',
          )}
          onClick={triggerFileInput}
        >
          Upload
        </span>
        <span
          className={cn(
            'text-smalldoge-3 font-bold text-msRed-1 cursor-pointer',
            disabled && 'pointer-events-none text-msGray-4',
          )}
          onClick={() => onImageUpdate(null, null)}
        >
          Remove Image
        </span>

        {/* Hidden input */}
        <input
          type="file"
          accept=".jpg,.jpeg,.png,.webp"
          className="hidden"
          disabled={disabled}
          ref={fileInputRef}
          onChange={handleUploadFile}
        />
      </div>
      {(error || errorText) && (
        <p className="mt-1 text-smalldoge-2 text-msError">
          {error || errorText}
        </p>
      )}
    </div>
  );
}
