import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const wrapperVariants = cva(
  'h-6 w-fit p-px rounded-3xl text-smalldoge-4 transition-all disabled:opacity-50 disabled:pointer-events-none group',
  {
    variants: {
      variant: {
        gray: 'bg-msGray-2',
        green: 'bg-msGreen-2',
        rainbow:
          'bg-[linear-gradient(to_right,#892CFF,#45B68D,#8FB8E9,#FF7979,#F8D247)]',
      },
    },
    defaultVariants: {
      variant: 'gray',
    },
  },
);

const shadowVariants = cva(
  'absolute w-6 h-6 blur-[15px] transition-all duration-500 transform -translate-y-[50%] -translate-x-[50%] left-0 top-[100%] group-hover:left-[100%] group-hover:top-0',
  {
    variants: {
      shadow: {
        green: 'bg-msGreen-2',
        yellow: 'bg-msYellow-2',
      },
    },
  },
);

const backgroundVariants = cva(
  'relative flex items-center space-x-1 overflow-hidden transition-all rounded-3xl w-full h-full px-2',
  {
    variants: {
      background: {
        white: 'bg-msWhite',
      },
    },
    defaultVariants: {
      background: 'white',
    },
  },
);

export interface ButtonMagicProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof wrapperVariants>,
    VariantProps<typeof shadowVariants>,
    VariantProps<typeof backgroundVariants> {
  asChild?: boolean;
}

const ButtonMagic = React.forwardRef<
  HTMLButtonElement,
  ButtonMagicProps & { children: React.ReactElement }
>(
  (
    {
      className,
      variant,
      shadow,
      background,
      asChild = false,
      children,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : 'button';
    return (
      <Comp
        className={cn(wrapperVariants({ variant, className }))}
        ref={ref}
        {...props}
      >
        <div className={cn(backgroundVariants({ background }))}>
          {children}
          <span className={cn(shadowVariants({ shadow }))} />
        </div>
      </Comp>
    );
  },
);
ButtonMagic.displayName = 'ButtonMagic';

export { ButtonMagic, wrapperVariants, shadowVariants, backgroundVariants };
