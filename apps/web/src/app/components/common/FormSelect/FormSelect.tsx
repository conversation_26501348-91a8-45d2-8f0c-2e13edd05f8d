import { Check, ChevronDown } from 'lucide-react';
import * as React from 'react';

import { ReadOnlyDisplay } from '../ReadOnlyDisplay';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components';
import { cn } from '@/lib/utils';

interface FormSelectProps {
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  inputPlaceholder?: string;
  emptyPlaceholder?: string;
  showSearch?: boolean;
  error?: string;
  isReadOnly?: boolean;
  disabled?: boolean;
  className?: string;
  options: {
    value: string;
    label: string;
  }[];
}

export function FormSelect({
  value,
  onValueChange,
  placeholder = 'Select an option',
  inputPlaceholder = 'Search...',
  emptyPlaceholder = 'No matches',
  showSearch = false,
  error,
  isReadOnly,
  disabled,
  className,
  options,
}: FormSelectProps) {
  const [open, setOpen] = React.useState(false);
  const selectedOption = options.find((opt) => opt.value === value);

  if (isReadOnly) {
    return (
      <div>
        <ReadOnlyDisplay
          value={selectedOption?.label}
          placeholder={placeholder}
          className={className}
        />
        {error && <p className="mt-1 text-smalldoge-2 text-msError">{error}</p>}
      </div>
    );
  }

  return (
    <div>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild disabled={disabled}>
          <div
            className={cn(
              'flex items-center justify-between cursor-pointer border border-input rounded-md px-3 py-2 text-sm',
              error && 'border-msRed-1',
              disabled && 'opacity-50 cursor-not-allowed',
              className,
            )}
          >
            <span
              className={cn(
                selectedOption ? 'text-foreground' : 'text-muted-foreground',
              )}
            >
              {selectedOption?.label || placeholder}
            </span>
            <ChevronDown className="w-4 h-4" />
          </div>
        </PopoverTrigger>
        <PopoverContent collisionPadding={10} className="w-[200px] p-0">
          <Command>
            {showSearch && (
              <CommandInput
                className="text-smalldoge-4"
                placeholder={inputPlaceholder}
              />
            )}
            <CommandList>
              <CommandEmpty className="text-smalldoge-4">
                {emptyPlaceholder}
              </CommandEmpty>
              <CommandGroup>
                {options.map((option) => (
                  <CommandItem
                    key={option.value}
                    className="text-smalldoge-4"
                    value={option.value}
                    onSelect={(currentValue) => {
                      onValueChange?.(
                        currentValue === value ? '' : currentValue,
                      );
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === option.value ? 'opacity-100' : 'opacity-0',
                      )}
                    />
                    {option.label}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
      {error && <p className="mt-1 text-smalldoge-2 text-msError">{error}</p>}
    </div>
  );
}
