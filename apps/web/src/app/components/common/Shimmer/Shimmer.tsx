import { cn } from '@/lib/utils';

interface ShimmerProps {
  className?: string;
  children?: React.ReactNode;
  isActive?: boolean;
}

export function Shimmer({
  className,
  children,
  isActive = true,
}: ShimmerProps) {
  if (!isActive) {
    return <>{children}</>;
  }

  return (
    <div className={cn('relative overflow-hidden', className)}>
      {children}
      <div className="absolute inset-0 pointer-events-none">
        <div
          className="absolute inset-0 shimmer-animation"
          style={{
            background:
              'repeating-linear-gradient(304.43deg, rgba(255, 255, 255, 0.1) 0%, rgba(196, 156, 245, 0.2) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(196, 156, 245, 0.2) 75%, rgba(255, 255, 255, 0.1) 100%)',
            backgroundSize: '200% 200%',
          }}
        />
      </div>
    </div>
  );
}
