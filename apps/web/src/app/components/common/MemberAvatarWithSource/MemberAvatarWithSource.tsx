import { RefreshCw, ClipboardPen } from 'lucide-react';
import { Member, MemberSource, memberSourceData } from 'shared/types';

import { Avatar } from '@/components/common/Avatar';
import { cn } from '@/lib/utils';

interface MemberAvatarWithSourceProps {
  member: Member;
}

export function MemberAvatarWithSource({
  member,
}: MemberAvatarWithSourceProps) {
  return (
    <div className="relative flex w-fit">
      <span
        className={cn(
          'w-10 h-10 rounded-full flex items-center justify-center mr-2',
          memberSourceData[member.source].color,
        )}
      >
        {member.source === MemberSource.cvinventory ? (
          <ClipboardPen size={20} />
        ) : (
          <img
            width="20"
            height="20"
            src={memberSourceData[member.source].icon}
            alt="source_logo"
          />
        )}
      </span>
      <Avatar size={40} url={member.avatar} />
      {member.source !== MemberSource.cvinventory && (
        <span className="absolute w-5 h-5 flex justify-center items-center bg-msGreen-2 rounded-full text-msWhite transform -translate-x-2.5 -translate-y-2.5 left-[50%] top-[50%]">
          <RefreshCw size={16} />
        </span>
      )}
    </div>
  );
}
