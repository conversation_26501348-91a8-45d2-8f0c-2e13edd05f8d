import { cn } from '@/lib/utils';

interface CvCountButtonProps {
  count: number;
  onClick?: (e: React.MouseEvent) => void;
  className?: string;
}

export function CvCountButton({
  count,
  onClick,
  className,
}: CvCountButtonProps) {
  const isZero = count === 0;

  return (
    <button
      onClick={onClick}
      className={cn(
        'relative flex flex-col items-center justify-between w-6 h-6 px-0 py-[3px] border border-msBlack rounded-[2px] transition-colors',
        isZero
          ? 'bg-msYellow-3 hover:bg-msYellow-2'
          : 'bg-msBlue-3 hover:bg-msBlue-4',
        className,
      )}
    >
      <div className="w-4 h-px bg-msBlack" />

      <span className="font-bold leading-none text-center text-smalldoge-4 text-msBlack">
        {count}
      </span>

      <div className="w-4 h-px bg-msBlack" />
    </button>
  );
}
