import { cn } from '@/lib/utils';

interface CustomerBadgeProps {
  name?: string;
  className?: string;
  onClick?: (e: React.MouseEvent) => void;
}

export function CustomerBadge({
  name,
  className,
  onClick,
}: CustomerBadgeProps) {
  return (
    <div
      className={cn(
        'w-fit bg-msGray-6 rounded-[100px] px-2',
        className,
        onClick && 'cursor-pointer hover:bg-msGray-5',
      )}
      onClick={onClick}
    >
      <span className="truncate text-smalldoge-3 text-msBlue-1">
        {name || 'No Client'}
      </span>
    </div>
  );
}
