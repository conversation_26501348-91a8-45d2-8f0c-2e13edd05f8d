import classNames from 'classnames';
import { createPortal } from 'react-dom';

import { Icon } from '../../../components';
import useOutsideClick from '../../../hooks/useOutsideClick';

import { Separator } from '@/components';

export interface DrawerProps {
  children: React.ReactNode;
  active: boolean;
  disabledOutsideClick?: boolean;
  className?: string;
  onClose: () => void;
  title?: string;
}

export function Drawer({
  children,
  active,
  disabledOutsideClick,
  className,
  onClose,
  title,
}: DrawerProps) {
  const containerRef = useOutsideClick<HTMLDivElement>(handleOutsideClick);

  function handleOutsideClick(event: MouseEvent | TouchEvent) {
    const targetElement = event.target as HTMLElement;

    if (
      active &&
      !disabledOutsideClick &&
      !targetElement.closest('.prevent-drawer-outside-click') &&
      !targetElement.classList.contains('prevent-drawer-outside-click')
    ) {
      onClose();
    }
  }

  return createPortal(
    <div
      ref={containerRef}
      className={classNames(
        'fixed top-0 h-dvh p-2 max-w-screen md:shadow-[-4px_0_6px_-1px_rgba(0,0,0,0.1)] z-50',
        'md:w-[680px] w-full transition-all duration-500 bg-msGray-6',
        active ? 'right-0' : 'delay-200 md:-right-[684px] -right-full',
        className,
      )}
    >
      <div className="flex flex-col w-full h-full p-4 rounded-sm sm:pb-4 bg-msWhite">
        <button className="block mb-4 outline-none w-fit" onClick={onClose}>
          <Icon source="doubleArrow" size={24} fillColor="msGray-3" />
        </button>
        {title && (
          <>
            <h2 className="font-bold text-smalldoge-1 text-msGray-3">
              {title}
            </h2>
            <Separator className="my-4" />
          </>
        )}
        <div className="flex-grow overflow-y-auto">{children}</div>
      </div>
    </div>,
    document.body,
  );
}
