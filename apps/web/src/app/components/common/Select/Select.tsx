import { Check } from 'lucide-react';
import * as React from 'react';

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components';
import { cn } from '@/lib/utils';

interface SelectProps {
  inputPlaceholder?: string;
  emptyPlaceholder?: string;
  showSearch?: boolean;
  activator: React.ReactElement;
  contentClassName?: string;
  selectedOption: string;
  options: {
    value: string;
    label: string;
  }[];
  onSelect: (value: string) => void;
}

export function Select({
  inputPlaceholder = 'Search...',
  emptyPlaceholder = 'No matches',
  showSearch,
  activator,
  contentClassName,
  selectedOption,
  options,
  onSelect,
}: SelectProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>{activator}</PopoverTrigger>
      <PopoverContent
        collisionPadding={10}
        className={cn('w-[200px] p-0', contentClassName)}
      >
        <Command>
          {showSearch && (
            <CommandInput
              className="text-smalldoge-4"
              placeholder={inputPlaceholder}
            />
          )}
          <CommandList>
            <CommandEmpty className="text-smalldoge-4">
              {emptyPlaceholder}
            </CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  className="text-smalldoge-4"
                  value={option.value}
                  onSelect={(currentValue) => {
                    onSelect(
                      currentValue === selectedOption ? '' : currentValue,
                    );
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      selectedOption === option.value
                        ? 'opacity-100'
                        : 'opacity-0',
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
