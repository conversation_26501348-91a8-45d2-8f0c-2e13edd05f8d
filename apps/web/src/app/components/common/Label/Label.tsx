import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import * as LabelPrimitive from '@radix-ui/react-label';
import { cva, type VariantProps } from 'class-variance-authority';
import { Ellipsis } from 'lucide-react';
import React from 'react';

import {
  ButtonSecondary,
  Checkbox,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../common';

import { cn } from '@/lib/utils';

const labelVariants = cva(
  'font-bold uppercase text-smalldoge-4 text-msGray-3 leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
);

const LabelBase = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &
    VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => (
  <LabelPrimitive.Root
    ref={ref}
    className={cn(labelVariants(), className)}
    {...props}
  />
));

export interface LabelProps {
  label: string;
  mandatory?: boolean;
  labelProps?: React.ComponentProps<'label'>;
  checkboxProps?: React.ComponentProps<typeof CheckboxPrimitive.Root>;
  dropDownItems?: { title: string; onClick: () => void }[];
}

export function Label({
  label,
  mandatory,
  labelProps,
  checkboxProps,
  dropDownItems,
}: LabelProps) {
  return (
    <div className="flex justify-between items-center">
      <LabelBase {...labelProps}>
        {label}
        {mandatory && <span className="text-msRed-1">*</span>}
      </LabelBase>
      <div className="flex space-x-2">
        {dropDownItems && (
          <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
              <ButtonSecondary variant={'icon'}>
                <Ellipsis size={14} />
              </ButtonSecondary>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              collisionPadding={10}
              align="end"
              className="w-32"
            >
              <DropdownMenuGroup>
                {dropDownItems.map((item, i) => (
                  <DropdownMenuItem key={i} onClick={item.onClick}>
                    <span className="text-smalldoge-3">{item.title}</span>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
        {checkboxProps && <Checkbox {...checkboxProps} />}
      </div>
    </div>
  );
}
