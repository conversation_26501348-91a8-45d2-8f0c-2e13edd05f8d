import { Input, InputProps } from '../../common';

import { cn } from '@/lib/utils';

interface InputAutoWidthProps {
  inputProps: InputProps;
  classNames?: string;
}

export function InputAutoWidth({
  inputProps,
  classNames,
}: InputAutoWidthProps) {
  // Use placeholder for width calculation if value is empty
  const displayValue = inputProps.value || inputProps.placeholder || '';

  return (
    <div className="relative rounded-sm bg-msGray-6">
      <span className="pl-3.5 pr-3 font-bold opacity-0 text-smalldoge-3">
        {displayValue}
      </span>
      <div className="absolute top-0 left-0">
        <Input
          {...inputProps}
          className={cn(
            'w-full h-6 font-bold border-0 shadow-none outline-none min-w-10 text-smalldoge-3 text-msGray-2 placeholder:text-msGray-3',
            classNames,
          )}
        />
      </div>
    </div>
  );
}
