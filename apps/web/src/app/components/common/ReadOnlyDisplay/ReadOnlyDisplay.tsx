import { cn } from '@/lib/utils';

interface ReadOnlyDisplayProps {
  value?: string | React.ReactNode;
  placeholder?: string;
  className?: string;
}

export function ReadOnlyDisplay({
  value,
  placeholder = 'N/A',
  className,
}: ReadOnlyDisplayProps) {
  return (
    <div
      className={cn('font-bold text-smalldoge-3 text-msBlack py-1', className)}
    >
      {value || placeholder}
    </div>
  );
}
