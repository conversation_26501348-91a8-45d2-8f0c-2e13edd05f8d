interface AvatarProps {
  url?: string | null;
  size?: number;
}

export function Avatar({ url, size = 48 }: AvatarProps) {
  return (
    <div
      className="flex-shrink-0 block bg-center bg-cover rounded-full"
      style={{
        width: size,
        height: size,
        //TODO: Remove dummy-user check when ms API will be fixed
        backgroundImage: `url("${
          !url || url === '/icons/dummy-user.png' ? '/images/person.png' : url
        }")`,
      }}
    />
  );
}
