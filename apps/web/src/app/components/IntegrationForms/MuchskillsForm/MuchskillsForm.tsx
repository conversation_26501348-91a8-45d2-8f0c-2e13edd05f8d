import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { Organization } from 'shared/types';
import { toast } from 'sonner';

import { ButtonSecondary, Input } from '@/components/common';
import { SettingsBadge } from '@/components/Settings/Badge/Badge';
import { useAuth } from '@/contexts/AuthContext';
import { connectToMuchskills, disconnectMuchskills } from '@/helpers/requests';

interface MuchskillsFormProps {
  organization: Organization;
}

export function MuchskillsForm({ organization }: MuchskillsFormProps) {
  const queryClient = useQueryClient();
  const { refreshUserData } = useAuth();
  const [token, setToken] = useState<string>('');

  const { mutate: updateToken, isPending: pendingUpdateToken } = useMutation({
    mutationFn: () => connectToMuchskills(organization._id, token),
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: ['organization'] });
      await refreshUserData();
      setToken('');
    },
    onError: (error) => toast.error(error.message),
  });

  const { mutate: disconnectMuchskillsIntegration } = useMutation({
    mutationFn: () => disconnectMuchskills(organization._id),
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['organizationFullInfo'],
      });
      await refreshUserData();
      setToken('');
    },
  });

  return (
    <div>
      <div className="flex items-center">
        <img
          width={24}
          src="/images/muchskills-logo-purple.png"
          alt="muchskills_logo"
        />
        <b className="text-smalldoge-1 ml-2">MuchSkills</b>
        <div className="ml-auto">
          {!organization?.muchskillsIntegration ? (
            <SettingsBadge variant="neutral">Not Connected</SettingsBadge>
          ) : organization.muchskillsIntegration.connected ? (
            <SettingsBadge variant="default">Connected</SettingsBadge>
          ) : (
            <SettingsBadge variant="destructive">Invalid Token</SettingsBadge>
          )}
        </div>
      </div>
      <span className="text-smalldoge-4 block mt-4">
        Connect your MuchSkills skills inventory and keep your skills profiles
        up to date for more accurate CV generation.
      </span>
      <div className="flex flex-col border-t border-msGray-5 mt-4 py-4">
        <div className="flex flex-col items-start space-y-1 sm:flex-row sm:items-center sm:space-x-1 sm:space-y-0 mb-4">
          <b className="flex-shrink-0 w-40 font-bold text-smalldoge-3 text-msGray-3">
            API Token
          </b>
          <Input
            value={token}
            placeholder={
              organization.muchskillsIntegration?.token || 'Your API Token'
            }
            className="text-smalldoge-3"
            wrapperClassName="flex-grow w-full"
            onChange={(e) => setToken(e.target.value)}
          />
        </div>
        <div className="flex space-x-2 ml-auto">
          {organization.muchskillsIntegration && (
            <ButtonSecondary
              className="ml-auto"
              onClick={() => disconnectMuchskillsIntegration()}
            >
              Disconnect
            </ButtonSecondary>
          )}
          <ButtonSecondary
            className="ml-auto"
            disabled={pendingUpdateToken || !token.length}
            onClick={() => updateToken()}
          >
            {organization.muchskillsIntegration ? 'Edit' : 'Set Token'}
          </ButtonSecondary>
        </div>
      </div>
    </div>
  );
}
