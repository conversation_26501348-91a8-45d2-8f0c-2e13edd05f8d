import {
  // Archive,
  // Chart<PERSON>ie,
  ChevronLeft,
  // LayoutTemplate,
  Puzzle,
  Settings,
  Building2,
  Users,
  UsersRound,
  CreditCard,
  Building,
  FileText,
  UserPen,
} from 'lucide-react';
import { ReactNode, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { WorkspaceDropdown } from './components';
import useOutsideClick from '../../hooks/useOutsideClick';

import { NAVIGATE_PATH, SETTINGS_PATH } from '@/helpers/constants';
import { cn } from '@/lib/utils';

const settingsSubmenu = [
  {
    title: 'Administration',
    items: [
      {
        title: 'API Integrations',
        icon: <Puzzle size={16} />,
        link: SETTINGS_PATH.integrations,
      },
      {
        title: 'Billing',
        icon: <CreditCard size={16} />,
        link: SETTINGS_PATH.billing,
      },
      {
        title: 'Team Settings',
        icon: <Building2 size={16} />,
        link: SETTINGS_PATH.organization,
      },
      {
        title: 'Members',
        icon: <UsersRound size={16} />,
        link: SETTINGS_PATH.organizationUsers,
      },
      {
        title: 'Profile Settings',
        icon: <UserPen size={16} />,
        link: SETTINGS_PATH.profileSettings,
      },
    ],
  },
  {
    title: 'Manage',
    items: [
      //   {
      //     title: 'Job Roles',
      //     icon: <LayoutTemplate size={16} />,
      //     link: SETTINGS_PATH.roles,
      //   },
      // {
      //   title: 'Customers',
      //   icon: <Users size={16} />,
      //   link: SETTINGS_PATH.roles,
      // },
    ],
  },
];

const mainMenu = [
  {
    title: 'Manage',
    items: [
      {
        title: 'CV Drafts',
        icon: <FileText size={16} />,
        link: NAVIGATE_PATH.drafts,
      },
      {
        title: 'Profiles',
        icon: <Users size={16} />,
        link: NAVIGATE_PATH.people,
      },
      {
        title: 'Clients',
        icon: <Building size={16} />,
        link: NAVIGATE_PATH.customers,
      },
      // {
      //   title: 'CV Templates',
      //   icon: <LayoutTemplate size={16} />,
      //   link: NAVIGATE_PATH.home,
      // },
    ],
  },
  {
    title: 'View',
    items: [
      // {
      //   title: 'Reports',
      //   icon: <ChartPie size={16} />,
      //   link: NAVIGATE_PATH.home,
      // },
      {
        title: 'Settings',
        icon: <Settings size={16} />,
        subMenu: settingsSubmenu,
      },
    ],
  },
];

interface MenuItem {
  title: string;
  items: {
    title: string;
    icon: ReactNode;
    link?: string;
    subMenu?: MenuItem[];
  }[];
}

interface NavigationProps {
  mobileMenuActive: boolean;
  onOutsideClick: () => void;
}

export function Navigation({
  mobileMenuActive,
  onOutsideClick,
}: NavigationProps) {
  const navigate = useNavigate();
  const location = useLocation();

  const [menuChunks, setMenuChunks] = useState<MenuItem[][]>([mainMenu]);
  const [activeMenuChunk, setActiveMenuChunk] = useState<number>(0);

  const containerRef = useOutsideClick<HTMLElement>(handleOutsideClick);

  function handleOutsideClick(event: MouseEvent | TouchEvent) {
    const targetElement = event.target as HTMLElement;

    if (
      mobileMenuActive &&
      !targetElement.closest('.prevent-nav-outside-click') &&
      !targetElement.classList.contains('prevent-nav-outside-click')
    ) {
      onOutsideClick();
    }
  }

  const openSubmenu = (submenu: MenuItem[]) => {
    setMenuChunks((prev) => [...prev, submenu]);
    setActiveMenuChunk((prev) => prev + 1);
  };

  return (
    <nav
      ref={containerRef}
      className={cn(
        'fixed z-50 h-[calc(100dvh-56px)] bottom-0 shadow-[4px_0_6px_-1px_rgba(0,0,0,0.1)] transition-left duration-200 ease-in-out',
        'md:relative md:z-auto md:h-screen md:left-0 w-56 bg-msGray-6 flex flex-col px-4 py-4 flex-shrink-0',
        mobileMenuActive ? 'left-0' : '-left-56',
      )}
    >
      {mobileMenuActive && (
        <img
          width={40}
          className="hidden mb-2 md:block"
          src="/images/logo.png"
          alt="logo"
        />
      )}
      <WorkspaceDropdown container={containerRef.current} />
      <div className="overflow-hidden">
        <div
          className="flex h-full transition-transform duration-300 ease-in-out"
          style={{ transform: `translateX(-${activeMenuChunk * 100}%)` }}
          onTransitionEnd={() =>
            setMenuChunks((prev) => prev.slice(0, activeMenuChunk + 1))
          }
        >
          {menuChunks.map((category, i) => (
            <div key={i} className="flex-shrink-0 w-full">
              {!!i && (
                <button
                  className="flex items-center mt-2"
                  onClick={() =>
                    setActiveMenuChunk((prev) => Math.max(prev - 1, 0))
                  }
                >
                  <ChevronLeft size={16} />
                  <b className="text-smalldoge-4">Back</b>
                </button>
              )}
              {category.map((item) => (
                <div key={item.title}>
                  <div className="flex items-center space-x-2 h-7 mx-1.5">
                    <span className="text-smalldoge-5 text-msGray-3">
                      {item.title}
                    </span>
                    <span className="flex-grow h-px bg-msGray-5" />
                  </div>
                  <div>
                    {item.items.map((item) => (
                      <div
                        key={item.title}
                        className={cn(
                          'flex space-x-2 items-center h-7 p-1.5 hover:bg-msGray-5 rounded-[4px] cursor-pointer',
                          item.link === location.pathname && 'bg-msWhite',
                        )}
                        onClick={() => {
                          if (item.subMenu) {
                            openSubmenu(item.subMenu);
                          }
                          if (item.link) {
                            navigate(item.link);
                          }
                        }}
                      >
                        {item.icon}
                        <span className="font-bold text-smalldoge-4">
                          {item.title}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </nav>
  );
}
