import { cn } from '@/lib/utils';

export function TypographyMuted({
  className,
  children,
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p className={cn('text-muted-foreground text-smalldoge-4', className)}>
      {children}
    </p>
  );
}

export function TypographyLarge({
  className,
  children,
}: React.HTMLAttributes<HTMLParagraphElement>) {
  return (
    <p className={cn('font-bold text-smalldoge-2', className)}>{children}</p>
  );
}

export function TypographyPageTitle({
  className,
  children,
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h1 className={cn('text-smalldoge-2 font-bold', className)}>{children}</h1>
  );
}

export function TypographySectionTitle({
  className,
  children,
}: React.HTMLAttributes<HTMLHeadingElement>) {
  return (
    <h2 className={cn('text-smalldoge-3 font-bold', className)}>{children}</h2>
  );
}

export function TypographyLabel({
  className,
  children,
}: React.HTMLAttributes<HTMLSpanElement>) {
  return (
    <span className={cn('text-smalldoge-4 font-bold', className)}>
      {children}
    </span>
  );
}
