import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

const settingsBadgeVariants = cva(
  'rounded-full text-smalldoge-3 px-2 py-0.5 font-bold w-fit h-fit inline-flex items-center justify-center',
  {
    variants: {
      variant: {
        default: 'bg-msBlue-3 text-msBlue-1',
        neutral: 'bg-msGray-5 text-msGray-3',
        destructive: 'bg-msRed-3 text-msRed-1',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

export interface SettingsBadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof settingsBadgeVariants> {}

export function SettingsBadge({
  className,
  variant,
  children,
  ...props
}: SettingsBadgeProps) {
  return (
    <div
      className={cn(settingsBadgeVariants({ variant }), className)}
      {...props}
    >
      {children}
    </div>
  );
}
