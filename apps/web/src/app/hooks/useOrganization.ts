import { useQuery } from '@tanstack/react-query';
import { Organization } from 'shared/types';

import api from '@/api';

export const useOrganization = () => {
  const {
    data: organization,
    isLoading: organizationLoading,
    refetch: refetchOrganization,
  } = useQuery<Organization>({
    queryKey: ['organization'],
    queryFn: async () => {
      const response = await api.get('/organization');
      return response.data;
    },
  });

  return { organization, organizationLoading, refetchOrganization };
};
