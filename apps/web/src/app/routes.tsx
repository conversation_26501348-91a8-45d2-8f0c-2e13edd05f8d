import { Route, Routes, Navigate } from 'react-router-dom';

import AuthRoute from './components/AuthRoute/AuthRoute';
import Layout from './components/Layout/Layout';
import NotFoundRedirect from './components/NotFoundRedirect';
import OrganizationRequiredRoute from './components/OrganizationRequiredRoute/OrganizationRequiredRoute';
import * as Pages from './pages';

import { NAVIGATE_PATH, SETTINGS_PATH } from '@/helpers/constants';

// Main routes component
export const AppRoutes = () => {
  return (
    <Routes>
      {/* Route for showing the accept invite page */}
      <Route
        path={NAVIGATE_PATH.acceptInvite}
        element={<Pages.AcceptInvitePage />}
      />

      {/* Auth routes */}
      <Route element={<AuthRoute />}>
        <Route path={NAVIGATE_PATH.login} element={<Pages.LoginPage />} />
        <Route path={NAVIGATE_PATH.signUp} element={<Pages.SignUpPage />} />
        <Route
          path={NAVIGATE_PATH.passwordReset}
          element={<Pages.PasswordResetPage />}
        />
        <Route
          path={NAVIGATE_PATH.setNewPassword}
          element={<Pages.SetNewPasswordPage />}
        />
        <Route
          path={NAVIGATE_PATH.emailVerification}
          element={<Pages.EmailVerificationPage />}
        />
      </Route>

      {/* Organization Creation route */}
      <Route
        path={NAVIGATE_PATH.createOrganization}
        element={<Pages.CreateOrganizationPage />}
      />

      {/* Protected routes - require organization */}
      <Route element={<OrganizationRequiredRoute />}>
        <Route path="/" element={<Layout />}>
          {/* Default route - redirect to home */}
          <Route index element={<Navigate to={NAVIGATE_PATH.home} replace />} />
          <Route path={NAVIGATE_PATH.people} element={<Pages.PeoplePage />} />
          <Route
            path={`${NAVIGATE_PATH.member}/:memberId`}
            element={<Pages.MemberPage />}
          />
          <Route
            path={NAVIGATE_PATH.customers}
            element={<Pages.CustomersPage />}
          />
          <Route path={NAVIGATE_PATH.drafts} element={<Pages.DraftsPage />} />
          <Route path={NAVIGATE_PATH.home} element={<Pages.PeoplePage />} />
          <Route path={NAVIGATE_PATH.settings}>
            <Route
              path={SETTINGS_PATH.integrations}
              element={<Pages.IntegrationsPage />}
            />
            <Route
              path={SETTINGS_PATH.billing}
              element={<Pages.BillingPage />}
            />
            <Route
              path={SETTINGS_PATH.organizationUsers}
              element={<Pages.OrganizationUsersPage />}
            />
            <Route
              path={SETTINGS_PATH.organization}
              element={<Pages.OrganizationSettingsPage />}
            />
            <Route
              path={SETTINGS_PATH.profileSettings}
              element={<Pages.ProfileSettingsPage />}
            />
          </Route>
        </Route>
      </Route>
      {/* Catch-all for non-existent routes */}
      <Route path="*" element={<NotFoundRedirect />} />
    </Routes>
  );
};
