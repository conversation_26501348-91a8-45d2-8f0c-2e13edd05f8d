// Common timezones using Intl.supportedValuesOf('timeZone')
// We'll filter to show the most commonly used ones for better UX
export const getCommonTimezones = () => {
  try {
    // Get all supported timezones
    const allTimezones = Intl.supportedValuesOf('timeZone');

    // Filter to common/major timezones for better UX
    const commonTimezonePatterns = [
      'America/New_York',
      'America/Chicago',
      'America/Denver',
      'America/Los_Angeles',
      'America/Toronto',
      'America/Vancouver',
      'America/Sao_Paulo',
      'America/Mexico_City',
      'Europe/London',
      'Europe/Paris',
      'Europe/Berlin',
      'Europe/Rome',
      'Europe/Madrid',
      'Europe/Amsterdam',
      'Europe/Stockholm',
      'Europe/Warsaw',
      'Europe/Kiev',
      'Europe/Moscow',
      'Asia/Tokyo',
      'Asia/Shanghai',
      'Asia/Hong_Kong',
      'Asia/Singapore',
      'Asia/Mumbai',
      'Asia/Dubai',
      'Asia/Seoul',
      'Australia/Sydney',
      'Australia/Melbourne',
      'Pacific/Auckland',
      'Africa/Cairo',
      'Africa/Johannesburg',
    ];

    // Filter and format timezones
    const commonTimezones = allTimezones
      .filter((tz) => commonTimezonePatterns.includes(tz))
      .map((tz) => ({
        value: tz,
        label: formatTimezoneLabel(tz),
      }))
      .sort((a, b) => a.label.localeCompare(b.label));

    return commonTimezones;
  } catch (error) {
    console.error('Error getting timezones:', error);
    // Fallback to a basic list if Intl.supportedValuesOf is not available
    return [
      { value: 'UTC', label: 'UTC' },
      { value: 'America/New_York', label: 'Eastern Time (New York)' },
      { value: 'America/Chicago', label: 'Central Time (Chicago)' },
      { value: 'America/Los_Angeles', label: 'Pacific Time (Los Angeles)' },
      { value: 'Europe/London', label: 'Greenwich Mean Time (London)' },
      { value: 'Europe/Paris', label: 'Central European Time (Paris)' },
      { value: 'Asia/Tokyo', label: 'Japan Standard Time (Tokyo)' },
    ];
  }
};

const formatTimezoneLabel = (timezone: string): string => {
  try {
    // Get the current offset for this timezone
    const now = new Date();
    const formatter = new Intl.DateTimeFormat('en', {
      timeZone: timezone,
      timeZoneName: 'short',
    });

    const parts = formatter.formatToParts(now);
    const timeZoneName =
      parts.find((part) => part.type === 'timeZoneName')?.value || '';

    // Format: "City (Timezone Abbreviation)"
    const city = timezone.split('/').pop()?.replace(/_/g, ' ') || timezone;
    return timeZoneName ? `${city} (${timeZoneName})` : city;
  } catch (error) {
    console.log('Error formatting timezone label:', error);
    return timezone.split('/').pop()?.replace(/_/g, ' ') || timezone;
  }
};
