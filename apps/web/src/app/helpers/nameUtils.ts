import { truncate } from 'lodash';

/**
 * Creates a full name from first and last name parts, handling empty values
 * @param firstName - First name (optional)
 * @param lastName - Last name (optional)
 * @returns Trimmed full name
 */
export function createFullName(firstName?: string, lastName?: string): string {
  return `${firstName || ''} ${lastName || ''}`.trim();
}

/**
 * Creates a truncated display name from first and last name parts using lodash truncate
 * @param firstName - First name (optional)
 * @param lastName - Last name (optional)
 * @param maxLength - Maximum length before truncation (default: 20)
 * @returns Truncated display name
 */
export function createTruncatedDisplayName(
  firstName?: string,
  lastName?: string,
  maxLength = 20,
): string {
  const fullName = createFullName(firstName, lastName);
  return truncate(fullName, { length: maxLength });
}
