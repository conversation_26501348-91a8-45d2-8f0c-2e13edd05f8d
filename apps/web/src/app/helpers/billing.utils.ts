import { PlanFeature, CustomStripeProductMetadata } from 'shared/types';

// Helper to format price (e.g., $25.00/month)
export const formatPrice = (
  amount: number,
  currency: string,
  interval = 'month',
): string => {
  return `${new Intl.NumberFormat('en-US', { style: 'currency', currency: currency.toUpperCase() }).format(amount / 100)}/${interval}`;
};

export const parseFeaturesFromJson = (featuresJson?: string): PlanFeature[] => {
  if (!featuresJson) return [];
  try {
    const featuresObject = JSON.parse(featuresJson);
    return Object.entries(featuresObject).map(([name, value]) => ({
      name,
      value: String(value === true ? 'Yes' : value === false ? 'No' : value), // Convert boolean to Yes/No
    }));
  } catch (error) {
    console.error('Error parsing features_json from metadata:', error);
    return [];
  }
};

export function extractFeaturesFromMetadata(
  metadata: CustomStripeProductMetadata,
): PlanFeature[] {
  const features: PlanFeature[] = [];
  if (metadata.profiles) {
    features.push({
      name: 'Profiles',
      value: metadata.profiles,
    });
  }
  if (metadata.max_cv) {
    features.push({ name: 'Max CVs', value: metadata.max_cv });
  }
  if (metadata.users) {
    features.push({ name: 'Users', value: metadata.users });
  }
  // Add other known feature keys from metadata here
  // For any other metadata fields that should be displayed as features:
  Object.entries(metadata).forEach(([key, value]) => {
    if (
      key !== 'id' &&
      key !== 'app' &&
      key !== 'profiles' &&
      key !== 'max_cv' &&
      key !== 'users' &&
      value
    ) {
      // Convert snake_case to Title Case for feature name
      const featureName = key
        .replace(/_/g, ' ')
        .replace(
          /\w\S*/g,
          (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase(),
        );
      features.push({ name: featureName, value: String(value) });
    }
  });

  if (features.length === 0) {
    features.push({ name: 'Feature set not defined', value: 'N/A' });
  }
  return features;
}
