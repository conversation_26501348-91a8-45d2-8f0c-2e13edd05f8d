import { useState } from 'react';
import { Link } from 'react-router-dom';

import { ButtonPrimary, Drawer, MuchskillsForm } from '@/components';
import { SettingsBadge } from '@/components/Settings/Badge/Badge';
import { useOrganization } from '@/hooks/useOrganization';

enum Integrations {
  muchskills = 'muchskills',
  // bamboo = 'bamboo',
}

export function IntegrationsPage() {
  const [drawerActive, setDrawerActive] = useState<boolean>(false);
  const [activeIntegration, setActiveIntegration] = useState<Integrations>();

  const { organization } = useOrganization();

  return (
    <>
      <div className="flex flex-col w-full md:max-w-[600px] mx-auto py-6 px-2">
        <div className="flex flex-col mb-6 space-y-2">
          <span className="font-bold text-smalldoge-2">API Integrations</span>
          <span className="text-smalldoge-4">
            Connect your existing team databases and directories to CV Inventory
          </span>
        </div>
        {/* MuchSkills Integration */}
        <div
          className="flex flex-col py-6 space-y-1 border-t border-b border-msGray-5 cursor-pointer hover:bg-msGray-6"
          onClick={() => {
            setDrawerActive(true);
            setActiveIntegration(Integrations.muchskills);
          }}
        >
          <div className="flex items-center space-x-2">
            <img
              width={24}
              src="/images/muchskills-logo-purple.png"
              alt="muchskills_logo"
            />
            <b className="flex-grow text-smalldoge-2">MuchSkills</b>
            {!organization?.muchskillsIntegration ? (
              <SettingsBadge variant="neutral">Not Connected</SettingsBadge>
            ) : organization.muchskillsIntegration.connected ? (
              <SettingsBadge variant="default">Connected</SettingsBadge>
            ) : (
              <SettingsBadge variant="destructive">Invalid Token</SettingsBadge>
            )}
          </div>
          <span className="text-smalldoge-4 text-msGray-3">
            Skills collection, mapping, visualisation and management platform
          </span>
          {!organization?.muchskillsIntegration && (
            <div className="p-2 mt-4 bg-msGray-6 rounded">
              <p className="text-smalldoge-4 text-msGray-3">
                Connect your MuchSkills account to CV Inventory for a seamless
                skills-based CV creation experience. If you do not have one, you
                can{' '}
                <ButtonPrimary
                  variant="link"
                  className="text-smalldoge-4"
                  asChild
                  onClick={(e) => e.stopPropagation()}
                >
                  <Link
                    to="https://www.muchskills.com/"
                    target="_blank"
                    rel="noreferrer"
                  >
                    sign up here
                  </Link>
                </ButtonPrimary>{' '}
                to and get started.
              </p>
            </div>
          )}
        </div>
        {/* <div className="flex flex-col py-6 space-y-1 border-t pointer-events-none border-msGray-5 grayscale">
          <div className="flex items-center space-x-2">
            <img width={24} src="/images/bamboo-logo.png" alt="bamboo_logo" />
            <b className="flex-grow text-smalldoge-2">Bamboo HR</b>
            {/* {false ? (
              <b className="text-smalldoge-3 text-msBlue-1">Synced</b>
            ) : (
              <b className="text-smalldoge-3 text-msGray-4">Not Synced</b>
            )} */}
        {/* <b className="text-smalldoge-3 text-msGray-4">Not Synced</b>
            <ButtonSecondary
              onClick={() => {
                setDrawerActive(true);
                setActiveIntegration(Integrations.bamboo);
              }}
            >
              Details
            </ButtonSecondary>
          </div>
          <span className="text-smalldoge-4 text-msGray-3">
            Ideal for tryouts, small agencies
          </span> */}
        {/* <div className="flex justify-between">
            <span className="text-smalldoge-4 text-msGray-3">User Synced</span>
            <b className="text-smalldoge-4">4000</b>
          </div> */}
        {/* </div> */}
      </div>
      <Drawer active={drawerActive} onClose={() => setDrawerActive(false)}>
        {organization &&
          (activeIntegration === Integrations.muchskills ? (
            <MuchskillsForm organization={organization} />
          ) : /* activeIntegration === Integrations.bamboo ? (
              <>Bamboo Form</>
            ) : */
          null)}
      </Drawer>
    </>
  );
}
