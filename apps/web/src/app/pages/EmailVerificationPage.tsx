import { Link, useLocation, useNavigate } from 'react-router-dom';

import { NAVIGATE_PATH } from '../helpers/constants';

import { Button, ButtonPrimary } from '@/components';

export function EmailVerificationPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const email = location.state?.email || 'your email';

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      {/* Header elements */}
      <div className="absolute top-4 left-4 md:top-8 md:left-8">
        <span className="text-sm font-bold text-foreground">CV Inventory</span>
      </div>
      <div className="absolute top-4 right-4 md:top-8 md:right-8">
        <Link to={NAVIGATE_PATH.login}>
          <Button variant="default" size="sm">
            Login
          </Button>
        </Link>
      </div>

      {/* Verification Message Container */}
      <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-foreground">
            Check Your Email
          </h1>
          <p className="text-sm text-muted-foreground">
            We've sent a confirmation email to{' '}
            <span className="font-medium">{email}</span>. Please check your
            inbox and click the link to verify your email address.
          </p>
        </div>

        <div className="space-y-4">
          <ButtonPrimary
            type="button"
            variant="white"
            className="w-full rounded-md"
            onClick={() => navigate(NAVIGATE_PATH.signUp)}
          >
            Change email
          </ButtonPrimary>

          <ButtonPrimary
            type="button"
            className="w-full rounded-md"
            onClick={() => navigate(NAVIGATE_PATH.login)}
          >
            Go to login
          </ButtonPrimary>
        </div>

        <p className="px-6 mt-8 text-xs text-center text-muted-foreground">
          Didn't receive the email? Check your spam folder or{' '}
          <Link
            to={NAVIGATE_PATH.signUp}
            className="underline hover:text-foreground"
          >
            try again
          </Link>
          .
        </p>
      </div>
    </div>
  );
}
