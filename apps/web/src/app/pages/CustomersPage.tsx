import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useState, useMemo, PropsWithChildren, useEffect } from 'react';
import { Customer } from 'shared/types';

import type { SortOption } from '@/components';

import { Separator, SortDropdown } from '@/components';
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Loader } from '@/components/common/Loader';
import { Search } from '@/components/common/Search/Search';
import {
  CreateCustomerDrawer,
  EditCustomerDrawer,
} from '@/components/Customers';
import { getCustomersRequest } from '@/helpers/requests';

type CustomerSortValue = 'name-asc' | 'name-desc' | 'date-asc' | 'date-desc';

type CustomerSortOption = SortOption & {
  value: CustomerSortValue;
};

const sortOptions: CustomerSortOption[] = [
  { value: 'name-asc', label: 'Name (A-Z)' },
  { value: 'name-desc', label: 'Name (Z-A)' },
  { value: 'date-desc', label: 'Newest First' },
  { value: 'date-asc', label: 'Oldest First' },
];

// Typography Components
const ListHeader: React.FC<PropsWithChildren<{ className?: string }>> = ({
  children,
  className = '',
}) => (
  <th
    className={`pb-1 pt-2 text-left text-smalldoge-5 font-normal text-msGray-3 ${className}`}
  >
    {children}
  </th>
);

const CustomerSubtext: React.FC<PropsWithChildren> = ({ children }) => (
  <div className="font-normal text-smalldoge-5 text-msGray-2">{children}</div>
);

export function CustomersPage() {
  const [isCreateDrawerOpen, setIsCreateDrawerOpen] = useState(false);
  const [isEditDrawerOpen, setIsEditDrawerOpen] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(
    null,
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<CustomerSortValue>('name-asc');
  const [selectedSort, setSelectedSort] = useState<CustomerSortOption>(
    sortOptions[0],
  );

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const customerId = params.get('customer');

    if (customerId) {
      setSelectedCustomerId(customerId);
      setIsEditDrawerOpen(true);

      const url = new URL(window.location.href);
      url.searchParams.delete('customer');
      window.history.replaceState(null, '', url.toString());
    }
  }, []);

  const { data: allCustomers, isLoading: isLoadingCustomers } = useQuery({
    queryKey: ['customers'],
    queryFn: getCustomersRequest,
  });

  const filteredAndSortedCustomers = useMemo(() => {
    const filtered =
      allCustomers?.filter((customer: Customer) =>
        customer.name.toLowerCase().includes(searchTerm.toLowerCase()),
      ) || [];

    return filtered.sort((a: Customer, b: Customer) => {
      switch (sortBy) {
        case 'name-asc':
          return a.name.localeCompare(b.name);
        case 'name-desc':
          return b.name.localeCompare(a.name);
        case 'date-asc':
          return (
            new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          );
        case 'date-desc':
          return (
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          );
        default:
          return 0;
      }
    });
  }, [allCustomers, searchTerm, sortBy]);

  const handleSortChange = (option: CustomerSortOption) => {
    setSelectedSort(option);
    setSortBy(option.value);
  };

  const selectedCustomer = useMemo(
    () => allCustomers?.find((customer) => customer._id === selectedCustomerId),
    [allCustomers, selectedCustomerId],
  );

  return (
    <div className="bg-msGray-7">
      <div>
        <div className="flex items-center justify-between p-2">
          <SortDropdown
            sortOptions={sortOptions}
            selectedSort={selectedSort}
            onSortChange={handleSortChange}
            align="start"
          />

          <div className="flex items-center">
            <Search
              value={searchTerm}
              onChange={setSearchTerm}
              placeholder="Search clients"
              bgColor="bg-msGray-6"
            />
            <ButtonPrimary
              variant="blackCompact"
              onClick={() => setIsCreateDrawerOpen(true)}
              className="px-2 py-1 ml-4"
            >
              Add Client
            </ButtonPrimary>
          </div>
        </div>

        <Separator />

        {isLoadingCustomers ? (
          <div className="flex items-center justify-center h-full py-10">
            <Loader />
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-msGray-5">
                  <ListHeader className="w-3/4 pl-4">Client Name</ListHeader>
                  <ListHeader className="w-1/4 pr-4 text-right">
                    Created
                  </ListHeader>
                </tr>
              </thead>
              <tbody>
                {filteredAndSortedCustomers &&
                filteredAndSortedCustomers.length > 0 ? (
                  filteredAndSortedCustomers.map((customer) => (
                    <tr
                      key={customer._id}
                      className="transition-colors duration-100 border-b cursor-pointer border-msGray-5 hover:bg-msGray-6"
                      onClick={() => {
                        setSelectedCustomerId(customer._id);
                        setIsEditDrawerOpen(true);
                      }}
                    >
                      <td className="p-4">
                        <div className="font-bold text-smalldoge-3 text-msBlack">
                          {customer.name}
                        </div>
                      </td>
                      <td className="p-4 text-right">
                        <CustomerSubtext>
                          {format(customer.createdAt, 'MMM dd, yyyy')}
                        </CustomerSubtext>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={2}
                      className="p-6 text-center text-msGray-3 text-smalldoge-3"
                    >
                      No clients found
                      {searchTerm && ' matching your search'}.
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <CreateCustomerDrawer
        isOpen={isCreateDrawerOpen}
        onClose={() => setIsCreateDrawerOpen(false)}
      />

      <EditCustomerDrawer
        isOpen={isEditDrawerOpen}
        onClose={() => {
          setIsEditDrawerOpen(false);
          setSelectedCustomerId(null);
        }}
        customer={selectedCustomer}
      />
    </div>
  );
}
