import { useMutation, useQuery } from '@tanstack/react-query';
import { uniqueId } from 'lodash';
import { ChevronLeft, Plus } from 'lucide-react';
import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useReducer,
  useState,
} from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import {
  AboutMeData,
  CertificationsData,
  CustomSection,
  EducationDataItem,
  LanguagesData,
  PersonalInfoData,
  Section,
  SectionData,
  SkillsData,
  WorkHistoryDataItem,
} from 'shared/types';
import { toast } from 'sonner';
import { useDebounce } from 'use-debounce';

import { CvList } from '../components';
import { createTruncatedDisplayName } from '../helpers/nameUtils';
import {
  getCvsByMemberRequest,
  getMemberByIdRequest,
  updateCvSectionsRequest,
} from '../helpers/requests';

import {
  ButtonSecondary,
  Drawer,
  CvForm,
  CvSettingsForm,
  CvPreview,
  ButtonPrimary,
  MemberBreadcrumb,
} from '@/components';
import { LayoutContext } from '@/components/Layout/Layout';
import { useResponsive } from '@/hooks/useResponsive';
import { cn } from '@/lib/utils';

export enum ReducerActionType {
  setInitialSections = 'setInitialSections',
  updateSection = 'updateSection',
  updateField = 'updateField',
  updateOrder = 'updateOrder',
  addSection = 'addSection',
  removeSection = 'removeSection',
  updateAiSections = 'updateAiSections',
  revertToSnapshot = 'revertToSnapshot',
}

export type FormReducerAction = {
  type: ReducerActionType;
  sectionId?: string;
  data?: SectionData;
  initialSections?: Section[];
  title?: string;
  active?: boolean;
  order?: number;
  aiSections?: any;
  customSections?: CustomSection[];
  updatedSections?: {
    // TODO(vladyslav): use Section type
    sections?: any;
    customSections?: CustomSection[];
  };
  snapshot?: Section[];
};

function sectionsReducer(sections: Section[], action: FormReducerAction) {
  switch (action.type) {
    case ReducerActionType.setInitialSections:
      return action.initialSections as Section[];

    case ReducerActionType.updateField:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return { ...section, data: action.data } as Section;
        }
        return section;
      });

    case ReducerActionType.updateSection:
      return sections.map((section) => {
        if (section.id === action.sectionId) {
          return {
            ...section,
            title: action.title,
            active: action.active,
          } as Section;
        }
        return section;
      });

    case ReducerActionType.updateOrder:
      return sections.map((section) => {
        if (section.id === action.sectionId && section.order !== action.order) {
          return { ...section, order: action.order } as Section;
        }
        return section;
      });

    case ReducerActionType.addSection:
      return sections.concat({
        id: 'customSection' + uniqueId(),
        order: sections.length + 1,
        title: 'Custom section',
        active: true,
        data: {
          description: '',
        },
      });

    case ReducerActionType.removeSection:
      return sections.filter((s) => s.id !== action.sectionId);

    case ReducerActionType.updateAiSections: {
      // Handle new updatedSections structure
      if (action.updatedSections) {
        // Update existing sections
        const updatedSections = sections.map((section) => {
          const aiSection = action.updatedSections?.sections?.[section.id];
          if (aiSection) {
            return {
              ...section,
              data: aiSection.data,
            };
          }
          return section;
        });

        // Add or update custom sections if provided
        if (
          action.updatedSections.customSections &&
          action.updatedSections.customSections.length > 0
        ) {
          // Remove existing custom sections and add new ones
          const nonCustomSections = updatedSections.filter(
            (s) => !s.id.includes('customSection'),
          );

          // Transform AI custom sections to match the expected format
          const transformedCustomSections =
            action.updatedSections.customSections.map((customSection) => ({
              ...customSection,
              id: customSection.id.includes('customSection')
                ? customSection.id
                : 'customSection' + customSection.id,
            }));

          return [...nonCustomSections, ...transformedCustomSections];
        }

        return updatedSections;
      }

      // Fallback to old structure for backward compatibility
      const updatedSections = sections.map((section) => {
        const aiSection = action.aiSections?.[section.id];
        if (aiSection) {
          return {
            ...section,
            data: aiSection.data,
          };
        }
        return section;
      });

      if (action.customSections && action.customSections.length > 0) {
        const nonCustomSections = updatedSections.filter(
          (s) => !s.id.includes('customSection'),
        );

        const transformedCustomSections = action.customSections.map(
          (customSection) => ({
            ...customSection,
            id: customSection.id.includes('customSection')
              ? customSection.id
              : 'customSection' + customSection.id,
          }),
        );

        return [...nonCustomSections, ...transformedCustomSections];
      }

      return updatedSections;
    }

    case ReducerActionType.revertToSnapshot:
      return action.snapshot ? [...action.snapshot] : sections;

    default:
      return sections;
  }
}

export function MemberPage() {
  const { memberId } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const { setHeaderCallback } = useContext(LayoutContext);
  const { isSmallScreen } = useResponsive();

  const [cvToPreview, setCvToPreview] = useState<string>();
  const [previewActive, setPreviewActive] = useState<boolean>(false);
  const [cvToEditSettings, setCvToEditSetting] = useState<string>();
  const [settingsActive, setSettingsActive] = useState<boolean>(false);
  const [cvEditMode, setCvEditMode] = useState<boolean>(false);
  const [sections, dispatchAction] = useReducer(sectionsReducer, []);
  const [debouncedSections] = useDebounce(sections, 600);
  const [preAiSnapshot, setPreAiSnapshot] = useState<Section[] | null>(null);
  const [hasAiChanges, setHasAiChanges] = useState<boolean>(false);

  const { data: member } = useQuery({
    enabled: !!memberId,
    queryKey: ['member', { memberId }],
    queryFn: () => getMemberByIdRequest(memberId as string),
  });

  const { data: memberCvs, isLoading: cvsLoading } = useQuery({
    enabled: !!memberId,
    queryKey: ['memberCvs', { memberId }],
    queryFn: () => getCvsByMemberRequest(memberId as string),
  });

  const { mutate: updateCvSections, mutateAsync: updateCvSectionsAsync } =
    useMutation({
      mutationFn: (cvId: string) =>
        updateCvSectionsRequest(cvId, {
          personalInfo: {
            ...(sections.find((s) => s.id === 'personalInfo') as Section),
            data: (sections.find((s) => s.id === 'personalInfo') as Section)
              .data as PersonalInfoData,
          },
          aboutMe: {
            ...(sections.find((s) => s.id === 'aboutMe') as Section),
            data: (sections.find((s) => s.id === 'aboutMe') as Section)
              .data as AboutMeData,
          },
          workHistory: {
            ...(sections.find((s) => s.id === 'workHistory') as Section),
            data: (sections.find((s) => s.id === 'workHistory') as Section)
              .data as WorkHistoryDataItem[],
          },
          education: {
            ...(sections.find((s) => s.id === 'education') as Section),
            data: (sections.find((s) => s.id === 'education') as Section)
              .data as EducationDataItem[],
          },
          certifications: {
            ...(sections.find((s) => s.id === 'certifications') as Section),
            data: (sections.find((s) => s.id === 'certifications') as Section)
              .data as CertificationsData,
          },
          skills: {
            ...(sections.find((s) => s.id === 'skills') as Section),
            data: (sections.find((s) => s.id === 'skills') as Section)
              .data as SkillsData,
          },
          languages: {
            ...(sections.find((s) => s.id === 'languages') as Section),
            data: (sections.find((s) => s.id === 'languages') as Section)
              .data as LanguagesData,
          },
          customSections: sections.filter((s) =>
            s.id.includes('customSection'),
          ) as CustomSection[],
        }),
      onSuccess: () => toast.success('CV updated'),
      onError: () => toast.error('Please fix invalid data before submitting.'),
    });

  useEffect(() => {
    if (member?.firstName || member?.lastName) {
      const displayName = createTruncatedDisplayName(
        member?.firstName,
        member?.lastName,
        20,
      );
      setHeaderCallback(`${displayName}'s CV`);
    } else {
      setHeaderCallback('CV');
    }

    return () => setHeaderCallback('');
  }, [setHeaderCallback, member?.firstName, member?.lastName]);

  const handleSetCvToEditSettings = useCallback((cvId: string) => {
    setCvToEditSetting(cvId);
    setSettingsActive(true);
    setCvToPreview(undefined);
    setPreviewActive(false);
  }, []);

  const handleCreateSnapshot = useCallback(() => {
    if (!preAiSnapshot) {
      setPreAiSnapshot([...sections]);
    }
  }, [sections, preAiSnapshot]);

  const handleRevertToSnapshot = useCallback(() => {
    if (preAiSnapshot) {
      dispatchAction({
        type: ReducerActionType.revertToSnapshot,
        snapshot: preAiSnapshot,
      });
      setHasAiChanges(false);
      // Trigger save after revert
      if (cvToPreview) {
        updateCvSections(cvToPreview);
      }
    }
  }, [preAiSnapshot, cvToPreview, updateCvSections]);

  // Handle query parameters for editCv and previewCv
  useEffect(() => {
    if (!memberCvs) return;

    const editCvId = searchParams.get('editCv');
    const previewCvId = searchParams.get('previewCv');

    if (editCvId) {
      const cvExists = memberCvs.find((cv) => cv._id === editCvId);
      if (cvExists) {
        handleSetCvToEditSettings(editCvId);
        // Remove the query parameter after handling
        searchParams.delete('editCv');
        setSearchParams(searchParams, { replace: true });
      }
    } else if (previewCvId) {
      const cvExists = memberCvs.find((cv) => cv._id === previewCvId);
      if (cvExists) {
        setCvToPreview(previewCvId);
        setPreviewActive(true);
        setCvToEditSetting(undefined);
        setSettingsActive(false);
        // Remove the query parameter after handling
        searchParams.delete('previewCv');
        setSearchParams(searchParams, { replace: true });
      }
    }
  }, [memberCvs, searchParams, setSearchParams, handleSetCvToEditSettings]);

  useEffect(() => {
    const cvToDisplay = memberCvs?.find((cv) => cv._id === cvToPreview);

    if (cvToDisplay) {
      const defaultSections = Object.entries(cvToDisplay.sections)
        .filter(([key, value]) => key !== '_id' && key !== 'customSections')
        .map(([key, value]) => ({
          id: key,
          ...value,
        }));

      const customSections = cvToDisplay.sections.customSections.map((sec) => ({
        id: 'customSection' + uniqueId(),
        ...sec,
      }));

      dispatchAction({
        type: ReducerActionType.setInitialSections,
        initialSections: [...defaultSections, ...customSections],
      });

      // Reset AI state when switching CVs
      setPreAiSnapshot(null);
      setHasAiChanges(false);
    }
  }, [memberCvs, cvToPreview]);

  const cvDataToChangeSettings = useMemo(
    () => memberCvs?.find((cv) => cv._id === cvToEditSettings),
    [memberCvs, cvToEditSettings],
  );

  const previewingCv = useMemo(
    () => memberCvs?.find((cv) => cv._id === cvToPreview),
    [memberCvs, cvToPreview],
  );

  if (!memberId) return null;

  return (
    <>
      <div className="flex h-full">
        <div className="flex flex-col w-full">
          {cvEditMode ? (
            <>
              <div className="flex h-10 px-4 py-2 border-b border-msGray-5">
                <ButtonSecondary
                  padding={'iconLeft'}
                  onClick={() => setCvEditMode(false)}
                >
                  <span className="flex items-center">
                    <ChevronLeft size={16} />
                    <span>Back to List</span>
                  </span>
                </ButtonSecondary>
                <div className="flex ml-auto space-x-2">
                  {isSmallScreen && (
                    <ButtonSecondary onClick={() => setPreviewActive(true)}>
                      Preview
                    </ButtonSecondary>
                  )}
                  {cvToPreview && (
                    <ButtonSecondary
                      onClick={() => updateCvSections(cvToPreview)}
                    >
                      Save Changes
                    </ButtonSecondary>
                  )}
                </div>
              </div>

              {/* CV Edit Mode Breadcrumb */}
              <div className="px-4 py-2 border-b border-msGray-5">
                <MemberBreadcrumb
                  memberName={createTruncatedDisplayName(
                    member?.firstName,
                    member?.lastName,
                    20,
                  )}
                  isSmallScreen={isSmallScreen}
                  mode="edit"
                  cvName={previewingCv?.preferences.title}
                  onMemberClick={() => setCvEditMode(false)}
                />
              </div>
              <CvForm
                sortedSections={sections.sort((a, b) => a.order - b.order)}
                onSectionUpdate={dispatchAction}
                addNewSection={() =>
                  dispatchAction({ type: ReducerActionType.addSection })
                }
              />
            </>
          ) : (
            <>
              <div className="flex items-center justify-between h-10 px-4 py-2 border-b border-msGray-5">
                <MemberBreadcrumb
                  memberName={createTruncatedDisplayName(
                    member?.firstName,
                    member?.lastName,
                    20,
                  )}
                  isSmallScreen={isSmallScreen}
                  mode="list"
                />
                <ButtonPrimary
                  variant="blackCompact"
                  className="flex items-center"
                  onClick={() => {
                    setCvToEditSetting(undefined);
                    setSettingsActive(true);
                    setCvToPreview(undefined);
                    setPreviewActive(false);
                  }}
                >
                  <Plus size={16} />
                  New CV
                </ButtonPrimary>
              </div>
              <CvList
                memberCvs={memberCvs}
                previewingCv={cvToPreview}
                memberId={memberId}
                isCvsLoading={cvsLoading}
                onCvRemoved={() => {
                  setCvToPreview(undefined);
                  setPreviewActive(false);
                  setCvToEditSetting(undefined);
                  setSettingsActive(false);
                }}
                onCvEdit={handleSetCvToEditSettings}
                onCvPreview={(cvId) => {
                  setCvToPreview(cvId);
                  setPreviewActive(true);
                  setCvToEditSetting(undefined);
                  setSettingsActive(false);
                }}
                onNewCvClick={() => {
                  setCvToEditSetting(undefined);
                  setSettingsActive(true);
                  setCvToPreview(undefined);
                  setPreviewActive(false);
                }}
              />
            </>
          )}
        </div>

        {!isSmallScreen && (
          <div
            className={cn(
              'hidden border-l border-msGray-5 w-[640px] flex-shrink-0',
              (settingsActive || previewActive) && 'block',
            )}
          >
            {previewActive && previewingCv && member && (
              <CvPreview
                key={previewingCv._id}
                cv={previewingCv}
                member={member}
                sections={debouncedSections}
                isEdit={cvEditMode}
                onEdit={() => setCvEditMode(true)}
                onSettingsEdit={() =>
                  handleSetCvToEditSettings(previewingCv._id)
                }
                onBeforeCvUpdate={() => updateCvSectionsAsync(previewingCv._id)}
                onAiSectionsUpdate={(response) => {
                  // Only update sections if they exist (handle bad prompt case)
                  if (response.updatedSections) {
                    dispatchAction({
                      type: ReducerActionType.updateAiSections,
                      updatedSections: response.updatedSections,
                    });
                    setHasAiChanges(true);
                  }
                }}
                onCreateSnapshot={handleCreateSnapshot}
                onRevertToSnapshot={handleRevertToSnapshot}
                hasAiChanges={hasAiChanges}
              />
            )}
            {settingsActive && (
              <div className="p-5">
                <CvSettingsForm
                  memberId={memberId}
                  initialData={cvDataToChangeSettings}
                  onCanceled={() => {
                    setSettingsActive(false);
                    setCvToEditSetting(undefined);
                  }}
                  onSubmitted={(cvId) => {
                    if (cvId) {
                      setCvToPreview(cvId);
                      setPreviewActive(true);
                    }

                    setSettingsActive(false);
                    setCvToEditSetting(undefined);
                  }}
                />
              </div>
            )}
          </div>
        )}
      </div>

      {isSmallScreen && (
        <>
          <Drawer
            active={previewActive}
            onClose={() => setPreviewActive(false)}
          >
            {previewingCv && member && (
              <CvPreview
                key={previewingCv._id}
                cv={previewingCv}
                member={member}
                sections={debouncedSections}
                isEdit={cvEditMode}
                onEdit={() => {
                  setCvEditMode(true);
                  setPreviewActive(false);
                }}
                onSettingsEdit={() =>
                  handleSetCvToEditSettings(previewingCv._id)
                }
                onBeforeCvUpdate={() => updateCvSectionsAsync(previewingCv._id)}
                onAiSectionsUpdate={(response) => {
                  // Only update sections if they exist (handle bad prompt case)
                  if (response.updatedSections) {
                    dispatchAction({
                      type: ReducerActionType.updateAiSections,
                      updatedSections: response.updatedSections,
                    });
                    setHasAiChanges(true);
                  }
                }}
                onCreateSnapshot={handleCreateSnapshot}
                onRevertToSnapshot={handleRevertToSnapshot}
                hasAiChanges={hasAiChanges}
              />
            )}
          </Drawer>
          <Drawer
            active={settingsActive}
            onClose={() => setSettingsActive(false)}
          >
            <div className="overflow-auto">
              <CvSettingsForm
                memberId={memberId}
                initialData={cvDataToChangeSettings}
                onCanceled={() => {
                  setSettingsActive(false);
                  setCvToEditSetting(undefined);
                }}
                onSubmitted={(cvId) => {
                  if (cvId) {
                    setCvToPreview(cvId);
                    setPreviewActive(true);
                  }

                  setSettingsActive(false);
                  setCvToEditSetting(undefined);
                }}
              />
            </div>
          </Drawer>
        </>
      )}
    </>
  );
}
