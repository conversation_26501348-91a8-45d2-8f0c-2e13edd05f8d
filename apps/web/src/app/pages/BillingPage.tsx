import { useQuery, useMutation } from '@tanstack/react-query';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';
import {
  ApiStripeProduct,
  Plan,
  StripePlanMapping,
  Organization,
} from 'shared/types';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components';
import { PlansList, BillingPageSkeleton } from '@/components/Billing'; // Updated import
import { ButtonPrimary } from '@/components/common/ButtonPrimary';
import { Separator } from '@/components/common/Separator';
import { Switch } from '@/components/common/Switch/Switch';
import {
  TypographyMuted,
  TypographyPageTitle,
  TypographyLabel,
} from '@/components/Settings';
import {
  getOrganizationRequest,
  createStripePortalSessionRequest,
  getStripeProductsRequest,
} from '@/helpers/requests';

enum BillingInterval {
  MONTH = 'month',
  YEAR = 'year',
}
type Currency = 'usd' | 'eur';

const currencyOptions: { value: Currency; label: string }[] = [
  { value: 'usd', label: 'USD' },
  { value: 'eur', label: 'EUR' },
];

export function BillingPage() {
  const [billingInterval, setBillingInterval] = useState<BillingInterval>(
    BillingInterval.MONTH,
  );
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>('eur');

  const {
    data: organization,
    isLoading: isLoadingOrganization,
    error: organizationError,
  } = useQuery<Organization, Error>({
    queryKey: ['organization'],
    queryFn: getOrganizationRequest,
  });

  const {
    data: stripeProducts,
    isLoading: isLoadingProducts,
    error: productsError,
  } = useQuery<ApiStripeProduct[], Error>({
    queryKey: ['stripeProducts'],
    queryFn: getStripeProductsRequest,
  });

  const createPortalSessionMutation = useMutation<{ url: string }, Error>({
    mutationFn: () => createStripePortalSessionRequest({}),
    onSuccess: (data) => {
      if (data.url) {
        window.location.href = data.url;
      } else {
        alert('Billing portal URL not returned.');
      }
    },
    onError: (error) => {
      alert(`Error creating billing portal session: ${error.message}`);
    },
  });

  const handleManageBilling = () => {
    createPortalSessionMutation.mutate();
  };

  const handleUpgrade = async (plan: Plan) => {
    if (!plan.priceId) {
      alert('No priceId set for this plan.');
      return;
    }
    try {
      const res = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          priceId: plan.priceId,
          currency: selectedCurrency,
        }),
      });
      if (!res.ok) throw new Error('Failed to create checkout session');
      const data = await res.json();
      if (data.url) {
        window.location.href = data.url;
      } else {
        alert('Stripe session URL not returned.');
      }
    } catch (err) {
      if (err instanceof Error) {
        alert('Error initiating payment: ' + err.message);
      } else {
        alert('Error initiating payment.');
      }
    }
  };

  if (isLoadingOrganization || isLoadingProducts) {
    return <BillingPageSkeleton />;
  }

  if (organizationError) {
    return (
      <div className="p-6">
        <TypographyPageTitle>Billing & Subscription</TypographyPageTitle>
        <p className="text-red-500">
          Error loading billing details: {organizationError.message}
        </p>
      </div>
    );
  }

  if (productsError) {
    return (
      <div className="p-6">
        <TypographyPageTitle>Billing & Subscription</TypographyPageTitle>
        <p className="text-red-500">
          Error loading plans: {productsError.message}
        </p>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="p-6">
        <TypographyPageTitle>Billing & Subscription</TypographyPageTitle>
        <p>No organization data returned from the backend.</p>
      </div>
    );
  }

  return (
    <div className="w-full md:max-w-[600px] mx-auto py-6 px-2 flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between">
          <TypographyPageTitle>Billing</TypographyPageTitle>
          <div
            title={
              !organization?.stripeCustomerId
                ? 'No active subscription found to manage.'
                : 'Manage your billing details'
            }
            className="inline-block"
          >
            <ButtonPrimary
              variant="link"
              onClick={handleManageBilling}
              disabled={
                createPortalSessionMutation.isPending ||
                !organization?.stripeCustomerId
              }
              className="w-auto"
            >
              {createPortalSessionMutation.isPending
                ? 'Loading...'
                : 'Manage Billing'}
            </ButtonPrimary>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Switch
              checked={billingInterval === BillingInterval.YEAR}
              onCheckedChange={(isChecked) =>
                setBillingInterval(
                  isChecked ? BillingInterval.YEAR : BillingInterval.MONTH,
                )
              }
            />
            <TypographyMuted>
              Billed{' '}
              {billingInterval === BillingInterval.YEAR ? 'annualy' : 'monthly'}
            </TypographyMuted>
          </div>

          <div className="flex items-center gap-2">
            <TypographyMuted>Pricing in</TypographyMuted>
            <DropdownMenu modal={false}>
              <DropdownMenuTrigger asChild>
                <div className="flex items-center cursor-pointer bg-background text-foreground">
                  <TypographyLabel>
                    {selectedCurrency.toUpperCase()}
                  </TypographyLabel>
                  <ChevronDown className="w-4 h-4" />
                </div>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                collisionPadding={10}
                align="end"
                className="w-fit prevent-drawer-outside-click"
              >
                <DropdownMenuGroup>
                  {currencyOptions.map((option) => (
                    <DropdownMenuItem
                      key={option.value}
                      onClick={() => setSelectedCurrency(option.value)}
                    >
                      <TypographyLabel>{option.label}</TypographyLabel>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuGroup>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>

      <Separator />

      <PlansList
        stripeProducts={stripeProducts || []}
        billingInterval={billingInterval}
        selectedCurrency={selectedCurrency}
        isLoadingProducts={isLoadingProducts}
        productsError={productsError}
        onUpgrade={handleUpgrade}
        organizationPlanTier={organization.planTier as StripePlanMapping}
      />
    </div>
  );
}
