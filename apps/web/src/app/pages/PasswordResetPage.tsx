import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { z } from 'zod';

import { Input, ButtonPrimary, Button } from '../components';
import { NAVIGATE_PATH } from '../helpers/constants';
import { requestPasswordResetEmail } from '../helpers/requests';

const resetPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ResetPasswordFormValues = z.infer<typeof resetPasswordSchema>;

export function PasswordResetPage() {
  const navigate = useNavigate();

  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
  } = useForm<ResetPasswordFormValues>({
    resolver: zodResolver(resetPasswordSchema),
  });

  const mutation = useMutation({
    mutationFn: (email: string) => requestPasswordResetEmail(email),
    onSuccess: () => {
      setSuccessMessage(
        'If an account with that email exists, you’ll get a password reset email shortly.',
      );
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      setError('email', {
        message: message || 'Failed to send reset instructions',
      });
    },
  });

  const onSubmit = (data: ResetPasswordFormValues) => {
    mutation.mutate(data.email);
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-4 bg-msGray-6">
      {/* Header elements */}
      <div className="absolute top-4 left-4 md:top-8 md:left-8">
        <span className="text-sm font-bold text-foreground">CV Inventory</span>
      </div>
      <div className="absolute top-4 right-4 md:top-8 md:right-8">
        <Button
          variant="default"
          size="sm"
          onClick={() => navigate(NAVIGATE_PATH.login)}
        >
          Back to Login
        </Button>
      </div>

      {/* Password Reset Form Container */}
      <div className="w-full max-w-md p-10 mt-16 rounded-lg shadow-xl bg-card md:p-20 md:mt-0">
        <div className="flex justify-center">
          <img width={40} src="/images/logo.png" alt="logo" />
        </div>

        <div className="mb-8 text-center">
          <h1 className="mb-2 text-2xl font-black text-foreground">
            Reset Password
          </h1>
          <p className="text-sm text-muted-foreground">
            Enter your email address and we'll send you instructions to reset
            your password.
          </p>
        </div>

        {successMessage ? (
          <div className="space-y-4">
            <div className="p-4 text-center text-green-700 bg-green-100 rounded-md">
              {successMessage}
            </div>
            <ButtonPrimary
              className="w-full rounded-md"
              onClick={() => navigate(NAVIGATE_PATH.login)}
            >
              Return to Login
            </ButtonPrimary>
          </div>
        ) : (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <Input
                type="email"
                placeholder="<EMAIL>"
                className="w-full"
                {...register('email')}
                error={errors.email?.message}
              />
            </div>
            <ButtonPrimary
              type="submit"
              className="w-full rounded-md"
              disabled={mutation.isPending}
            >
              {mutation.isPending ? 'Sending...' : 'Send Reset Instructions'}
            </ButtonPrimary>
          </form>
        )}
      </div>
    </div>
  );
}
