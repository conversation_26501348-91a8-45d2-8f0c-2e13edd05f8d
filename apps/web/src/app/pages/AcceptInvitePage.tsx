import { useMutation, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

import { Button } from '@/components/common/Button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/common/Dialog';
import { useAuth } from '@/contexts/AuthContext';
import { NAVIGATE_PATH } from '@/helpers/constants';
import {
  acceptInviteRequest,
  getInviteDetailsRequest,
} from '@/helpers/requests';

export function AcceptInvitePage() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [open, setOpen] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const token = searchParams.get('token');
  const { refreshUserData } = useAuth();

  const {
    data: inviteDetails,
    isLoading: isInviteLoading,
    isError: isInviteError,
    // error: inviteError,
  } = useQuery({
    queryKey: ['invite-details', token],
    queryFn: () => (token ? getInviteDetailsRequest(token) : Promise.reject()),
    enabled: !!token,
    retry: false,
  });

  const mutation = useMutation({
    mutationFn: (token: string) => acceptInviteRequest(token),
    onSuccess: async () => {
      try {
        // Refresh user data to get updated availableOrganizations
        await refreshUserData();
        setOpen(false);
        navigate(NAVIGATE_PATH.home);
      } catch (error) {
        console.error(
          'Error refreshing user data after invite acceptance:',
          error,
        );
        // Still navigate even if refresh fails
        setOpen(false);
        navigate(NAVIGATE_PATH.home);
      }
    },
    onError: (error: AxiosError) => {
      const message = (error?.response?.data as { message: string })?.message;
      setError(message);
    },
  });

  useEffect(() => {
    if (!token) {
      setError('No invite token provided');
      setOpen(false);
    }
  }, [token]);

  const handleAccept = () => {
    if (inviteDetails?.isLoggedIn) {
      mutation.mutate(inviteDetails.inviteToken);
    } else {
      const path = inviteDetails?.userExists
        ? NAVIGATE_PATH.login
        : NAVIGATE_PATH.signUp;
      navigate(`${path}?token=${token}`);
    }
  };

  const handleDecline = () => {
    setOpen(false);
    navigate(NAVIGATE_PATH.home);
  };

  if (!token) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-msGray-6">
        <div className="p-6">
          <p className="text-destructive">No invite token provided</p>
        </div>
      </div>
    );
  }

  let description: React.ReactNode =
    'You have been invited to join an organization. Would you like to accept this invitation?';
  if (isInviteLoading) {
    description = 'Loading invite details...';
  } else if (isInviteError) {
    description = <p className="text-destructive">Invite not found</p>;
  } else if (inviteDetails) {
    if (inviteDetails.status === 'accepted') {
      description = (
        <p className="text-success">
          This invite has already been accepted for{' '}
          <b>{inviteDetails.organizationName}</b>.
        </p>
      );
    } else if (inviteDetails.isExpired) {
      description = (
        <p className="text-destructive">This invite has expired.</p>
      );
    } else {
      description = (
        <span>
          You have been invited to join <b>{inviteDetails.organizationName}</b>.
          Would you like to accept this invitation?
        </span>
      );
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-msGray-6">
      <Dialog open={open} onOpenChange={handleDecline}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Organization Invitation</DialogTitle>
            <DialogDescription>
              {error ? (
                <p className="text-destructive">{error}</p>
              ) : (
                description
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            {!(
              inviteDetails &&
              (inviteDetails.status === 'accepted' ||
                inviteDetails.isExpired ||
                error)
            ) && (
              <>
                <Button variant="outline" onClick={handleDecline}>
                  Decline
                </Button>
                <Button
                  onClick={handleAccept}
                  disabled={
                    mutation.isPending ||
                    isInviteLoading ||
                    isInviteError ||
                    (inviteDetails && inviteDetails.isExpired)
                  }
                >
                  {mutation.isPending ? 'Accepting...' : 'Accept'}
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
