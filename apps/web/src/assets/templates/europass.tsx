import { Document, Page, View, Text, Image, Font } from '@react-pdf/renderer';
import { format } from 'date-fns';
import React from 'react';
import Html from 'react-pdf-html';
import {
  Section,
  WorkHistoryDataItem,
  AboutMe,
  Certifications,
  CustomSection,
  Education,
  Languages,
  PersonalInfo,
  Skills,
  WorkHistory,
  EducationDataItem,
} from 'shared/types';

import { mail, pin, phone } from './icons';
import { getSocialIcon } from './socialIcons';

interface EuropassTemplateProps {
  avatar?: string;
  sections: Section[];
}

export function EuropassTemplate({ avatar, sections }: EuropassTemplateProps) {
  Font.register({
    family: 'Lato',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6uyw4BMUTPHvxk6XweuBCY.ttf',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u9w4BMUTPHh6UVew-FGC_p9dw.ttf',
        fontWeight: 'bold',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u8w4BMUTPHjxswWyWrFCbw7A.ttf',
        fontStyle: 'italic',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u_w4BMUTPHjxsI5wqPHA3s5dwt7w.ttf',
        fontWeight: 'bold',
        fontStyle: 'italic',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u9w4BMUTPHh50Xew-FGC_p9dw.ttf',
        fontWeight: 'heavy',
      },
      {
        src: 'https://fonts.gstatic.com/s/lato/v24/S6u_w4BMUTPHjxsI3wiPHA3s5dwt7w.ttf',
        fontWeight: 'heavy',
        fontStyle: 'italic',
      },
    ],
  });

  const personalInfoBlock = (section: PersonalInfo) => (
    <>
      <View
        wrap={false}
        style={{ marginBottom: 16, display: 'flex', flexDirection: 'row' }}
      >
        {avatar && (
          <Image
            style={{
              width: 112,
              minWidth: 112,
              height: 112,
              backgroundColor: 'gray',
              borderRadius: '100%',
              marginRight: 14,
              objectFit: 'cover',
            }}
            src={avatar}
          />
        )}
        <View
          style={{
            display: 'flex',
            flexDirection: 'column',
            paddingTop: 4,
            paddingBottom: 4,
          }}
        >
          <Text
            style={{
              marginBottom: 4,
              color: '#527AAA',
              fontSize: 18,
              fontWeight: 'bold',
            }}
          >
            {section.data.firstName.active &&
              section.data.firstName.value + ' '}
            {section.data.lastName.active && section.data.lastName.value}
          </Text>
          <View
            style={{
              display: 'flex',
              flexDirection: 'row',
              rowGap: 4,
              columnGap: 8,
              flexWrap: 'wrap',
              alignItems: 'center',
            }}
          >
            {section.data.nationality.value &&
              section.data.nationality.active && (
                <View>
                  <Text style={{ fontWeight: 'bold' }}>
                    Nationality: {section.data.nationality.value}
                  </Text>
                </View>
              )}
            {section.data.telephone.active && section.data.telephone.value && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  columnGap: 4,
                  flexWrap: 'nowrap',
                }}
              >
                {phone}
                <Text style={{ fontWeight: 'bold' }}>
                  {section.data.telephone.value}
                </Text>
              </View>
            )}
            {section.data.location.active && section.data.location.value && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  columnGap: 4,
                  flexWrap: 'nowrap',
                }}
              >
                {pin}
                <Text style={{ fontWeight: 'bold' }}>
                  {section.data.location.value}
                </Text>
              </View>
            )}
            {section.data.email.active && section.data.email.value && (
              <View
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  columnGap: 4,
                  flexWrap: 'nowrap',
                }}
              >
                {mail}
                <Text style={{ fontWeight: 'bold' }}>
                  {section.data.email.value}
                </Text>
              </View>
            )}
            {section.data.socials.active &&
              section.data.socials.inputs.map((input, i) => (
                <View
                  key={i}
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    columnGap: 4,
                    flexWrap: 'nowrap',
                  }}
                >
                  {getSocialIcon(input)}
                  <Text style={{ fontWeight: 'bold' }}>{input}</Text>
                </View>
              ))}
          </View>
        </View>
      </View>
      {section.data.jobTitle.active && section.data.jobTitle.value && (
        <View wrap={false} style={{ paddingBottom: 16 }}>
          <Text
            style={{
              color: '#527AAA',
              paddingBottom: 4,
              borderBottom: '1pt solid #e0e0e0',
              textTransform: 'uppercase',
              marginBottom: 8,
              fontWeight: 'bold',
            }}
          >
            Position
          </Text>
          <Text>{section.data.jobTitle.value}</Text>
        </View>
      )}
    </>
  );

  const aboutMe = (section: AboutMe) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 16 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <Html
          style={{ fontFamily: 'Lato', fontSize: 10 }}
          renderers={{
            p: (val) => <Text>{val.children}</Text>,
            ol: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
            ul: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
          }}
        >
          {`${section.data.description}`}
        </Html>
      </View>
    );

  const workHistoryItem = (workHistRec: WorkHistoryDataItem) =>
    workHistRec.active && (
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <View
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <Text
            style={{ color: '#527AAA', maxWidth: '70%', fontWeight: 'bold' }}
          >
            {workHistRec.roleTitle}
          </Text>
          <Text style={{ fontWeight: 'bold' }}>
            {workHistRec.startDate && format(workHistRec.startDate, 'yyyy')}
            {' - '}
            {workHistRec.isCurrent
              ? 'Present'
              : workHistRec.endDate && format(workHistRec.endDate, 'yyyy')}
          </Text>
        </View>
        <Text style={{ fontWeight: 'bold' }}>{workHistRec.companyName}</Text>
        <View style={{ marginTop: 10 }}>
          <Html
            style={{ fontFamily: 'Lato', fontSize: 10 }}
            renderers={{
              p: (val) => <Text>{val.children}</Text>,
              ol: (val) => (
                <View style={{ marginLeft: -10 }}>{val.children}</View>
              ),
              ul: (val) => (
                <View style={{ marginLeft: -10 }}>{val.children}</View>
              ),
            }}
          >
            {`${workHistRec.description}`}
          </Html>
        </View>
      </View>
    );

  const workHistoryBlock = (section: WorkHistory) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 16 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <View style={{ display: 'flex', flexDirection: 'column', rowGap: 16 }}>
          {section.data
            .filter((h) => h.active)
            .map((histRec, i) => (
              <React.Fragment key={i}>
                {workHistoryItem(histRec)}
              </React.Fragment>
            ))}
        </View>
      </View>
    );

  const educationItem = (education: EducationDataItem) =>
    education.active && (
      <View
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <View
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            justifyContent: 'space-between',
          }}
        >
          <Text
            style={{ color: '#527AAA', maxWidth: '50%', fontWeight: 'bold' }}
          >
            {education.degree}
          </Text>{' '}
          <Text style={{ fontWeight: 'bold' }}>
            {education.startDate && format(education.startDate, 'yyyy')}
            {' - '}
            {education.endDate && format(education.endDate, 'yyyy')}
          </Text>
        </View>
        <Text style={{ fontWeight: 'bold' }}>{education.schoolName}</Text>
        <View style={{ marginTop: 10 }}>
          <Html
            style={{ fontFamily: 'Lato', fontSize: 10 }}
            renderers={{
              p: (val) => <Text>{val.children}</Text>,
              ol: (val) => (
                <View style={{ marginLeft: -10 }}>{val.children}</View>
              ),
              ul: (val) => (
                <View style={{ marginLeft: -10 }}>{val.children}</View>
              ),
            }}
          >
            {`${education.description}`}
          </Html>
        </View>
      </View>
    );

  const educationBlock = (section: Education) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 16 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1px solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <View style={{ display: 'flex', flexDirection: 'column', rowGap: 16 }}>
          {section.data
            .filter((h) => h.active)
            .map((edRec, i) => (
              <React.Fragment key={i}>{educationItem(edRec)}</React.Fragment>
            ))}
        </View>
      </View>
    );

  const certificationsBlock = (section: Certifications) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 12 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <Html
          style={{ fontFamily: 'Lato', fontSize: 10 }}
          renderers={{
            p: (val) => <Text>{val.children}</Text>,
            ol: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
            ul: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
          }}
        >
          {`${section.data.description}`}
        </Html>
      </View>
    );

  const keySkillsBlock = (section: Skills) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 12 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <Html
          style={{ fontFamily: 'Lato', fontSize: 10 }}
          renderers={{
            p: (val) => <Text>{val.children}</Text>,
            ol: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
            ul: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
          }}
        >
          {`${section.data.description}`}
        </Html>
      </View>
    );

  const languagesBlock = (section: Languages) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 16 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <Text>{section.data.languages.join(', ')}</Text>
      </View>
    );

  const customSection = (section: CustomSection) =>
    section.active && (
      <View wrap={false} style={{ paddingBottom: 16 }}>
        <Text
          style={{
            color: '#527AAA',
            paddingBottom: 4,
            borderBottom: '1pt solid #e0e0e0',
            textTransform: 'uppercase',
            marginBottom: 8,
            fontWeight: 'bold',
          }}
        >
          {section.title}
        </Text>
        <Html
          style={{ fontFamily: 'Lato', fontSize: 10 }}
          renderers={{
            p: (val) => <Text>{val.children}</Text>,
            ol: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
            ul: (val) => (
              <View style={{ marginLeft: -10 }}>{val.children}</View>
            ),
          }}
        >
          {`${section.data.description}`}
        </Html>
      </View>
    );

  const getSection = (section: Section) => {
    switch (section.id) {
      case 'personalInfo':
        return personalInfoBlock(section as PersonalInfo);
      case 'aboutMe':
        return aboutMe(section as AboutMe);
      case 'workHistory':
        return workHistoryBlock(section as WorkHistory);
      case 'education':
        return educationBlock(section as Education);
      case 'certifications':
        return certificationsBlock(section as Certifications);
      case 'skills':
        return keySkillsBlock(section as Skills);
      case 'languages':
        return languagesBlock(section as Languages);
      default:
        return customSection(section as CustomSection);
    }
  };

  return (
    <Document>
      <Page
        style={{
          padding: 18,
          fontFamily: 'Lato',
          fontSize: 10,
        }}
        size="A4"
      >
        {sections.map((section, i) => (
          <React.Fragment key={i}>{getSection(section)}</React.Fragment>
        ))}
      </Page>
    </Document>
  );
}
