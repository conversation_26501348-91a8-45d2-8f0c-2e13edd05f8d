import { Document, Page, View, Text, Font } from '@react-pdf/renderer';
import { format } from 'date-fns';
import React from 'react';
import Html from 'react-pdf-html';
import {
  Section,
  WorkHistoryDataItem,
  AboutMe,
  Certifications,
  CustomSection,
  Education,
  Languages,
  PersonalInfo,
  Skills,
  WorkHistory,
  EducationDataItem,
} from 'shared/types';

interface CleanTemplateProps {
  sections: Section[];
}

export function CleanTemplate({ sections }: CleanTemplateProps) {
  Font.register({
    family: 'Inter',
    fonts: [
      {
        src: 'https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuLyfAZ9hjQ.ttf',
        fontWeight: 400,
      },
      {
        src: 'https://fonts.gstatic.com/s/inter/v19/UcCO3FwrK3iLTeHuS_nVMrMxCp50SjIw2boKoduKmMEVuFuYAZ9hjQ.ttf',
        fontWeight: 700,
      },
    ],
  });

  const styles = {
    name: {
      fontFamily: 'Inter',
      fontWeight: 700,
      fontSize: 32,
      lineHeight: 1.21,
      color: '#000000',
      textTransform: 'uppercase' as const,
    },
    jobTitle: {
      fontFamily: 'Inter',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.21,
      letterSpacing: 0.2,
      color: '#000000',
      textTransform: 'uppercase' as const,
    },
    sectionHeading: {
      fontFamily: 'Inter',
      fontWeight: 700,
      fontSize: 13,
      lineHeight: 1.21,
      letterSpacing: 0.26,
      color: '#000000',
      textTransform: 'uppercase' as const,
      textDecoration: 'underline' as const,
    },
    workTitle: {
      fontFamily: 'Inter',
      fontWeight: 700,
      fontSize: 10,
      lineHeight: 1.21,
      letterSpacing: 0.2,
      color: '#000000',
    },
    dateText: {
      fontFamily: 'Inter',
      fontWeight: 700,
      fontSize: 9,
      lineHeight: 1.21,
      letterSpacing: 0.09,
      color: '#000000',
    },
    bodyText: {
      fontFamily: 'Inter',
      fontWeight: 400,
      fontSize: 9,
      lineHeight: 1.21,
      color: 'rgba(0, 0, 0, 0.7)',
    },
    descriptionText: {
      fontFamily: 'Inter',
      fontWeight: 400,
      fontSize: 9,
      lineHeight: 1.44,
      color: 'rgba(0, 0, 0, 0.7)',
    },
    contactText: {
      fontFamily: 'Inter',
      fontWeight: 400,
      fontSize: 8,
      lineHeight: 1.21,
      color: 'rgba(0, 0, 0, 0.6)',
    },
    skillText: {
      fontFamily: 'Inter',
      fontWeight: 400,
      fontSize: 9,
      lineHeight: 1.21,
      letterSpacing: 0.09,
      color: 'rgba(0, 0, 0, 0.7)',
    },
  };

  // Contact info component with dot separators
  const ContactInfo = ({ personalInfo }: { personalInfo: PersonalInfo }) => {
    const contactItems = [];

    if (personalInfo.data.location.active && personalInfo.data.location.value) {
      contactItems.push(personalInfo.data.location.value);
    }
    if (personalInfo.data.email.active && personalInfo.data.email.value) {
      contactItems.push(personalInfo.data.email.value);
    }
    if (
      personalInfo.data.telephone.active &&
      personalInfo.data.telephone.value
    ) {
      contactItems.push(personalInfo.data.telephone.value);
    }

    return (
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          marginTop: 8,
          marginBottom: 24,
        }}
      >
        {contactItems.map((item, index) => (
          <React.Fragment key={index}>
            <Text style={styles.contactText}>{item}</Text>
            {index < contactItems.length - 1 && (
              <View
                style={{
                  width: 3,
                  height: 3,
                  backgroundColor: '#000000',
                  borderRadius: 1.5,
                  marginHorizontal: 10,
                }}
              />
            )}
          </React.Fragment>
        ))}
      </View>
    );
  };

  // Progress bar component for skills - commented out for now
  // const ProgressBar = ({ percentage }: { percentage: number }) => {
  //   const safePercentage =
  //     isNaN(percentage) || percentage < 0 ? 0 : Math.min(percentage, 100);

  //   return (
  //     <View
  //       style={{
  //         width: 184,
  //         height: 3,
  //         backgroundColor: '#D9D9D9',
  //         marginTop: 6,
  //         marginBottom: 10,
  //       }}
  //     >
  //       <View
  //         style={{
  //           width: `${safePercentage}%`,
  //           height: 3,
  //           backgroundColor: '#000000',
  //         }}
  //       />
  //     </View>
  //   );
  // };

  // Personal info section
  const personalInfoBlock = (section: PersonalInfo) => (
    <View>
      <Text style={styles.name}>
        {section.data.firstName.active && section.data.firstName.value + ' '}
        {section.data.lastName.active && section.data.lastName.value}
      </Text>
      {section.data.jobTitle.active && section.data.jobTitle.value && (
        <Text style={styles.jobTitle}>{section.data.jobTitle.value}</Text>
      )}
      <ContactInfo personalInfo={section} />
    </View>
  );

  // About me section with grid layout
  const aboutMeBlock = (section: AboutMe) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.sectionHeading}>{section.title}</Text>
          </View>
          <View style={{ flex: 1 }}>
            <Html
              style={{
                fontFamily: 'Inter',
                fontSize: 9,
                lineHeight: 1.21,
                color: 'rgba(0, 0, 0, 0.7)',
              }}
              renderers={{
                p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
              }}
            >
              {`${section.data.description}`}
            </Html>
          </View>
        </View>
      </View>
    );

  // Work history item with grid-like layout
  const workHistoryItem = (workHistRec: WorkHistoryDataItem) =>
    workHistRec.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.dateText}>
              {workHistRec.startDate &&
                format(workHistRec.startDate, 'MMM yyyy')}
              {' - '}
              {workHistRec.isCurrent
                ? 'Present'
                : workHistRec.endDate &&
                  format(workHistRec.endDate, 'MMM yyyy')}
            </Text>
          </View>
          <View style={{ flex: 1 }}>
            <Text style={styles.workTitle}>
              {workHistRec.roleTitle}, {workHistRec.companyName}
            </Text>
            <View style={{ marginTop: 8 }}>
              <Html
                style={{
                  fontFamily: 'Inter',
                  fontSize: 9,
                  lineHeight: 1.44,
                  color: 'rgba(0, 0, 0, 0.7)',
                }}
                renderers={{
                  p: (val) => (
                    <Text style={styles.descriptionText}>{val.children}</Text>
                  ),
                }}
              >
                {`${workHistRec.description}`}
              </Html>
            </View>
          </View>
        </View>
      </View>
    );

  // Work history section
  const workHistoryBlock = (section: WorkHistory) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <Text style={styles.sectionHeading}>{section.title}</Text>
        <View style={{ marginTop: 16 }}>
          {section.data
            .filter((h) => h.active)
            .map((histRec, i) => (
              <React.Fragment key={i}>
                {workHistoryItem(histRec)}
              </React.Fragment>
            ))}
        </View>
      </View>
    );

  // Education item with grid-like layout
  const educationItem = (education: EducationDataItem) =>
    education.active && (
      <View style={{ marginBottom: 16 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.dateText}>
              {education.startDate && format(education.startDate, 'MMM yyyy')}
              {' - '}
              {education.endDate && format(education.endDate, 'MMM yyyy')}
            </Text>
          </View>
          <View style={{ flex: 1 }}>
            <Text style={styles.workTitle}>{education.schoolName}</Text>
            <Text style={styles.skillText}>{education.degree}</Text>
          </View>
        </View>
      </View>
    );

  // Education section
  const educationBlock = (section: Education) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <Text style={styles.sectionHeading}>{section.title}</Text>
        <View style={{ marginTop: 16 }}>
          {section.data
            .filter((h) => h.active)
            .map((edRec, i) => (
              <React.Fragment key={i}>{educationItem(edRec)}</React.Fragment>
            ))}
        </View>
      </View>
    );

  // Skills section with progress bars - commented out for now
  // const skillsBlock = (section: Skills) =>
  //   section.active && (
  //     <View style={{ marginBottom: 24 }}>
  //       <Text style={styles.sectionHeading}>{section.title}</Text>
  //       <View style={{ marginTop: 16 }}>
  //         {/* For now, we'll parse skills from description and assign random percentages */}
  //         {/* In a real implementation, you'd want to modify the Skills type to include skill levels */}
  //         {[
  //           'Figma',
  //           'Sketch',
  //           'Adobe Photoshop',
  //           'Adobe Illustrator',
  //           'Principle',
  //           'Adobe XD',
  //         ].map((skill, index) => {
  //           const percentages = [95, 95, 77, 52, 63, 63]; // Based on Figma design
  //           const percentage = percentages[index] || 50; // Default to 50% if undefined
  //           return (
  //             <View key={index}>
  //               <Text style={styles.skillText}>{skill}</Text>
  //               <ProgressBar percentage={percentage} />
  //             </View>
  //           );
  //         })}
  //       </View>
  //     </View>
  //   );

  // Skills section with grid layout
  const skillsBlock = (section: Skills) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.sectionHeading}>{section.title}</Text>
          </View>
          <View style={{ flex: 1 }}>
            <Html
              style={{
                fontFamily: 'Inter',
                fontSize: 9,
                color: 'rgba(0, 0, 0, 0.7)',
              }}
              renderers={{
                p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
              }}
            >
              {`${section.data.description}`}
            </Html>
          </View>
        </View>
      </View>
    );

  // Certifications section with grid layout
  const certificationsBlock = (section: Certifications) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.sectionHeading}>{section.title}</Text>
          </View>
          <View style={{ flex: 1 }}>
            <Html
              style={{
                fontFamily: 'Inter',
                fontSize: 9,
                color: 'rgba(0, 0, 0, 0.7)',
              }}
              renderers={{
                p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
              }}
            >
              {`${section.data.description}`}
            </Html>
          </View>
        </View>
      </View>
    );

  const languagesBlock = (section: Languages) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.sectionHeading}>{section.title}</Text>
          </View>
          <View style={{ flex: 1 }}>
            <Text style={styles.bodyText}>
              {section.data.languages.join(', ')}
            </Text>
          </View>
        </View>
      </View>
    );

  const customSection = (section: CustomSection) =>
    section.active && (
      <View style={{ marginBottom: 24 }}>
        <View
          style={{
            flexDirection: 'row',
            alignItems: 'flex-start',
          }}
        >
          <View style={{ width: 140, marginRight: 20 }}>
            <Text style={styles.sectionHeading}>{section.title}</Text>
          </View>
          <View style={{ flex: 1 }}>
            <Html
              style={{
                fontFamily: 'Inter',
                fontSize: 9,
                color: 'rgba(0, 0, 0, 0.7)',
              }}
              renderers={{
                p: (val) => <Text style={styles.bodyText}>{val.children}</Text>,
              }}
            >
              {`${section.data.description}`}
            </Html>
          </View>
        </View>
      </View>
    );

  const getSection = (section: Section) => {
    switch (section.id) {
      case 'personalInfo':
        return personalInfoBlock(section as PersonalInfo);
      case 'aboutMe':
        return aboutMeBlock(section as AboutMe);
      case 'workHistory':
        return workHistoryBlock(section as WorkHistory);
      case 'education':
        return educationBlock(section as Education);
      case 'certifications':
        return certificationsBlock(section as Certifications);
      case 'skills':
        return skillsBlock(section as Skills);
      case 'languages':
        return languagesBlock(section as Languages);
      default:
        return customSection(section as CustomSection);
    }
  };

  return (
    <Document>
      <Page
        style={{
          padding: 40,
          fontFamily: 'Inter',
          fontSize: 9,
          backgroundColor: '#FFFFFF',
        }}
        size="A4"
      >
        {sections.map((section, i) => (
          <React.Fragment key={i}>{getSection(section)}</React.Fragment>
        ))}
      </Page>
    </Document>
  );
}
