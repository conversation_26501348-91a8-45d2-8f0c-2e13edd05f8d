const { join } = require('path');

const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');

/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    join(
      __dirname,
      '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}',
    ),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  safelist: [{ pattern: /^fill-.*/ }, { pattern: /^bg-.*/ }],
  theme: {
    screens: {
      sm: '576px',
      md: '836px',
      lg: '1264px',
      xl: '1366px',
    },
    fontFamily: {
      sans: ['Lato', 'sans-serif'],
    },
    fontWeight: {
      black: '900',
      bold: '700',
      regular: '400',
    },
    fontSize: {
      'bigdoge-1': ['3.75rem', '120%'],
      'bigdoge-2': ['2.5rem', '120%'],
      'bigdoge-3': ['2rem', '125%'],
      'bigdoge-4': ['1.75rem', '130%'],
      'bigdoge-5': ['1.5rem', '135%'],
      'bigdoge-6': ['1.25rem', '140%'],
      'bigdoge-7': ['1rem', '125%'],
      'smalldoge-1': ['1.125rem', '135%'],
      'smalldoge-2': ['1rem', '150%'],
      'smalldoge-3': ['0.875rem', '140%'],
      'smalldoge-4': ['0.75rem', '130%'],
      'smalldoge-5': ['0.625rem', '160%'],
    },
    colors: {
      msWhite: {
        DEFAULT: '#ffffff',
      },
      msBlack: {
        DEFAULT: '#000000',
      },
      msGray: {
        1: '#181818',
        2: '#333333',
        3: '#666666',
        4: '#afafaf',
        5: '#e0e0e0',
        6: '#f2f2f2',
      },
      msRed: {
        1: '#a55454',
        2: '#e4aaaa',
        3: '#ffcfcf',
      },
      msGreen: {
        1: '#375e50',
        2: '#45b68d',
        3: '#4ceab1',
        4: '#b7fee4',
      },
      msBlue: {
        1: '#527aaa',
        2: '#8fb8e9',
        3: '#cfe5ff',
        4: '#f3f9ff',
      },
      msYellow: {
        1: '#f8d247',
        2: '#f6e39e',
        3: '#f6ecc9',
      },
      msPink: {
        1: '#de3f4e',
        2: '#ff7979',
      },
      msMagicAi: {
        1: '#7100FF',
        2: '#E3CCFF',
        3: '#F2E7FF',
      },
      transparent: {
        DEFAULT: 'transparent',
      },
    },
    extend: {
      spacing: {
        3.5: '0.875rem',
        4.5: '1.125rem',
        17: '4.25rem',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
