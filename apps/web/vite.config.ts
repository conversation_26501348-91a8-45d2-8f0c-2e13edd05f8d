/// <reference types='vitest' />
import path from 'path';

import { nxCopyAssetsPlugin } from '@nx/vite/plugins/nx-copy-assets.plugin';
import { nxViteTsPaths } from '@nx/vite/plugins/nx-tsconfig-paths.plugin';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import svgr from 'vite-plugin-svgr';

let proxyConfig = {};
try {
  const apiUrl = new URL(
    process.env.VITE_API_BASE_URL || 'http://localhost:3333',
  );
  const apiPath = apiUrl.pathname === '/' ? '/api' : apiUrl.pathname;
  const apiBaseWithoutPath = `${apiUrl.protocol}//${apiUrl.host}`;

  proxyConfig = {
    [apiPath]: {
      target: apiBaseWithoutPath,
      changeOrigin: true,
    },
  };
} catch (error) {
  console.warn(
    'Invalid VITE_API_BASE_URL, using default proxy configuration',
    error,
  );
  proxyConfig = {
    '/api': {
      target: 'http://localhost:3333',
      changeOrigin: true,
    },
  };
}

export default defineConfig({
  root: __dirname,
  cacheDir: '../../node_modules/.vite/apps/web',
  server: {
    port: parseInt(process.env.VITE_WEB_PORT || '4200'),
    proxy: proxyConfig,
  },
  preview: {
    port: 4300,
    host: 'localhost',
  },
  plugins: [
    react(),
    nxViteTsPaths(),
    nxCopyAssetsPlugin(['*.md']),
    svgr({ include: '**/*.svg' }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src/app'),
      shared: path.resolve(__dirname, '../shared'),
    },
  },
  // Uncomment this if you are using workers.
  // worker: {
  //  plugins: [ nxViteTsPaths() ],
  // },
  build: {
    outDir: '../../dist/apps/web',
    emptyOutDir: true,
    reportCompressedSize: true,
    commonjsOptions: {
      transformMixedEsModules: true,
    },
  },
});
