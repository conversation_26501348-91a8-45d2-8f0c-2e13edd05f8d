{
  "extends": "../../tsconfig.base.json",
  "files": [],
  "include": [],
  "references": [
    {
      "path": "./tsconfig.app.json"
    },
    {
      "path": "./tsconfig.spec.json"
    }
  ],
  "compilerOptions": {
    "baseUrl": ".",
    "esModuleInterop": true,
    "paths": {
      "shared/*": ["../shared/*"]
    },
     //remove it when dummy data won't be needed anymore
     "resolveJsonModule": true,
  }
}
