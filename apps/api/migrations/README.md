# Database Migrations

This directory contains database migration scripts using `migrate-mongo-ts`.

## Available Commands

```bash
# Check migration status
yarn migrate:status

# Run pending migrations
yarn migrate:up

# Rollback last migration
yarn migrate:down

# Create new migration
yarn migrate:create <migration-name>
```

## Current Migrations

### 20250625211825-remove-client-strings-from-cvs.ts
**Purpose**: Remove old `client` string fields from CV preferences and prepare for customer ObjectId references.

**What it does**:
- Finds all CVs with `preferences.client` field
- Removes the `client` field from all CVs
- Sets customer field to null (handled by application code)

**Rollback**: Adds empty `client` strings back to CVs that were migrated.

## Running Migrations

### Manual Execution (Current Setup)
```bash
# 1. Check what migrations are pending
yarn migrate:status

# 2. Run migrations
yarn migrate:up

# 3. Verify application works correctly
yarn nx serve api
```

### Before Running Migrations
1. **Backup your database** (recommended)
2. **Test in staging environment first**
3. **Ensure application is stopped** during migration
4. **Have rollback plan ready**

### After Running Migrations
1. **Verify migration completed successfully**
2. **Test application functionality**
3. **Check that CVs no longer have client strings**
4. **Verify customer dropdown works in CV form**

## Environment Variables

Migrations use the same environment variables as the application:
- `MONGO_URI` - MongoDB connection string

## Troubleshooting

### Migration Fails
```bash
# Check migration status
yarn migrate:status

# If needed, rollback
yarn migrate:down

# Fix the issue and try again
yarn migrate:up
```

### Database Connection Issues
- Verify `MONGO_URI` environment variable
- Ensure MongoDB is running
- Check network connectivity

## Future: Automated Migrations

See `for-ai/automated-migration-implementation-plan.md` for details on implementing automated migrations as part of the deployment process.