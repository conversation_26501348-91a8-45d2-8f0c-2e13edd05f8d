import { Db } from 'mongodb';

export async function up(db: Db) {
  console.log('Starting migration: removing client strings from CVs...');

  // Find all CVs that have the old 'client' field
  const cvsWithClientStrings = await db
    .collection('cvs')
    .find({
      'preferences.client': { $exists: true },
    })
    .toArray();

  console.log(`Found ${cvsWithClientStrings.length} CVs with client strings`);

  if (cvsWithClientStrings.length > 0) {
    // Remove the 'client' field from all CVs
    const result = await db
      .collection('cvs')
      .updateMany(
        { 'preferences.client': { $exists: true } },
        { $unset: { 'preferences.client': '' } },
      );

    console.log(`Updated ${result.modifiedCount} CVs - removed client strings`);
  }

  console.log('Migration completed successfully');
}

export async function down(db: Db) {
  console.log(
    'Rolling back migration: this will set all client fields to empty string...',
  );

  // Find CVs that don't have client field (were migrated)
  const migratedCvs = await db
    .collection('cvs')
    .find({
      'preferences.client': { $exists: false },
      preferences: { $exists: true },
    })
    .toArray();

  console.log(`Found ${migratedCvs.length} CVs to rollback`);

  if (migratedCvs.length > 0) {
    // Add back empty client field for rollback
    const result = await db.collection('cvs').updateMany(
      {
        'preferences.client': { $exists: false },
        preferences: { $exists: true },
      },
      { $set: { 'preferences.client': '' } },
    );

    console.log(
      `Rolled back ${result.modifiedCount} CVs - added empty client strings`,
    );
  }

  console.log('Rollback completed');
}
