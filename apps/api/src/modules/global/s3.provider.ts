import { Injectable } from '@nestjs/common';
import AWS from 'aws-sdk';
import S3, { Body } from 'aws-sdk/clients/s3';
import { ManagedUpload } from 'aws-sdk/lib/s3/managed_upload';

@Injectable()
export class S3Provider {
  private readonly s3: S3;

  constructor() {
    this.s3 = new AWS.S3({
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      },
      region: process.env.AWS_REGION,
    });
  }

  async save(body: Body, contentType: string, key: string) {
    const params = {
      ContentType: contentType,
      Bucket: process.env.AWS_BUCKET || '',
      Key: key + new Date().getTime(),
      Body: body,
    };

    return await new Promise<ManagedUpload.SendData>((resolve, reject) => {
      this.s3
        .upload(params)
        .promise()
        .then(
          (data) => {
            resolve(data);
          },
          (err) => {
            console.log('err', err);
            reject(err);
          },
        );
    });
  }

  async delete(key: string) {
    const params = {
      Bucket: process.env.AWS_BUCKET || '',
      Key: key,
    };

    try {
      await this.s3.deleteObject(params).promise();
      console.log(`File deleted: ${key}`);
    } catch (error) {
      console.error(`Failed to delete file: ${key}`, error);
    }
  }
}
