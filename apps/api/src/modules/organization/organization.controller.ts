import {
  Controller,
  Delete,
  Get,
  Post,
  Put,
  Param,
  Query,
  Body,
  UseGuards,
} from '@nestjs/common';
import { UserRole, OrganizationFullInfo } from 'shared/types';

import {
  CreateOrganizationDto,
  UpdateOrganizationDto,
  DeleteOrganizationDto,
} from './dto';
import { Organization } from './organization.schema';
import { OrganizationService } from './organization.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  @UseGuards(AuthGuard)
  async getOrganization(@AuthUser() user: AuthUserDto) {
    const { _id: orgId } = user.organization;

    return await this.organizationService.findById(orgId);
  }

  @Post()
  @UseGuards(AuthGuard)
  async createOrganization(
    @AuthUser() user: AuthUserDto,
    @Body() createOrganizationDto: CreateOrganizationDto,
  ) {
    const organization = await this.organizationService.createOrganization(
      createOrganizationDto,
    );

    await this.organizationService.addUserToOrganization(
      organization._id,
      user._id,
      UserRole.OWNER,
    );

    await this.organizationService.setActiveOrganization(
      user._id,
      organization._id,
    );

    // Clear deletion scheduled date if it exists
    await this.organizationService.usersService.markUserForDeletion(
      user._id,
      null,
    );

    return organization;
  }

  @Get('full-info')
  @UseGuards(AuthGuard)
  getOrganizationFullInfo(
    @AuthUser() user: AuthUserDto,
  ): Promise<OrganizationFullInfo> {
    return this.organizationService.getOrganizationFullInfo(
      user.organization._id,
    );
  }

  @Put()
  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Update, subject: Organization })
  updateOrganization(
    @AuthUser() user: AuthUserDto,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
  ) {
    return this.organizationService.updateOrganization(
      user.organization._id,
      updateOrganizationDto,
    );
  }

  @Get('users')
  @UseGuards(AuthGuard)
  getOrganizationUsers(
    @AuthUser() user: AuthUserDto,
    @Query('search') searchTerm?: string,
  ) {
    return this.organizationService.getOrganizationUsers(
      user.organization._id,
      searchTerm,
    );
  }

  @Delete()
  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Delete, subject: Organization })
  deleteOrganization(
    @AuthUser() user: AuthUserDto,
    @Body() deleteOrganizationDto: DeleteOrganizationDto,
  ) {
    return this.organizationService.deleteOrganization(
      user.organization._id,
      user._id,
      deleteOrganizationDto.password,
    );
  }

  @Post('cancel-deletion')
  @UseGuards(AuthGuard, PoliciesGuard)
  @CheckAbilities({ action: Action.Delete, subject: Organization })
  cancelOrganizationDeletion(
    @AuthUser() user: AuthUserDto,
  ): Promise<Organization | null> {
    return this.organizationService.cancelOrganizationDeletion(
      user.organization._id,
    );
  }

  @Post('set-active/:organizationId')
  @UseGuards(AuthGuard)
  setActiveOrganization(
    @AuthUser() user: AuthUserDto,
    @Param('organizationId') organizationId: string,
  ) {
    return this.organizationService.setActiveOrganization(
      user._id,
      organizationId,
    );
  }

  @Get('all')
  @UseGuards(AuthGuard)
  getAllOrganizations(@AuthUser() user: AuthUserDto) {
    return this.organizationService.getUserOrganizations(user._id);
  }
}
