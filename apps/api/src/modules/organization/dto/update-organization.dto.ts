import {
  Is<PERSON>ptional,
  Is<PERSON>tring,
  Is<PERSON>rray,
  <PERSON>Length,
  ArrayMaxSize,
} from 'class-validator';

export class UpdateOrganizationDto {
  @IsOptional()
  @IsString()
  @MaxLength(30)
  name?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  description?: string;

  @IsOptional()
  @IsString()
  @MaxLength(50)
  website?: string;

  @IsOptional()
  @IsString()
  timezone?: string;

  @IsOptional()
  @IsArray()
  @ArrayMaxSize(5)
  @IsString({ each: true })
  locations?: string[];

  @IsOptional()
  @IsString()
  photo?: string;
}
