import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import { RegenerateCvDto } from 'shared/dto';

import { Cv } from './cv.schema';
import { CvsService } from './cvs.service';
import { CvPreferencesDto, UpdateCvDto, GetCvsDto } from './dto';
import { Member } from '../members/member.schema';
import { UpdateCvSectionsDto } from './dto/update-cv-sections.dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType } from '../stripe/guards/plan-limit.guard';
import { PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@UseGuards(AuthGuard, PoliciesGuard)
@Controller('cvs')
export class CvsController {
  constructor(private cvsService: CvsService) {}

  @UseGuards(PlanLimitGuard)
  @CheckPlanLimit(LimitType.CVS) // Apply decorator
  @Post('create/:memberId')
  @CheckAbilities({ action: Action.Create, subject: Cv })
  async createCv(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
    @Body() dto: CvPreferencesDto,
  ) {
    const newCv = await this.cvsService.createCv(
      memberId,
      user.organization._id,
      dto,
      user,
    );

    return newCv;
  }

  @Post('update/:cvId')
  @CheckAbilities({ action: Action.Update, subject: Cv })
  async updateCv(
    @AuthUser() user: AuthUserDto,
    @Param('cvId') cvId: string,
    @Body() dto: UpdateCvDto,
  ) {
    return await this.cvsService.updateCv(cvId, dto, user);
  }

  @Post('update-sections/:cvId')
  @CheckAbilities({ action: Action.Update, subject: Cv })
  async updateSectionsCv(
    @AuthUser() user: AuthUserDto,
    @Param('cvId') cvId: string,
    @Body() dto: UpdateCvSectionsDto,
  ) {
    return await this.cvsService.updateCvSections(cvId, dto, user);
  }

  @Post('duplicate/:cvId')
  @CheckAbilities({ action: Action.Create, subject: Cv })
  async duplicateCv(
    @AuthUser() user: AuthUserDto,
    @Param('cvId') cvId: string,
  ) {
    return await this.cvsService.duplicateCv(cvId, user);
  }

  @Delete('delete/:cvId')
  @CheckAbilities({ action: Action.Delete, subject: Cv })
  async deleteCv(@AuthUser() user: AuthUserDto, @Param('cvId') cvId: string) {
    return await this.cvsService.deleteCv(cvId, user);
  }

  @Get('byMember/:memberId')
  @CheckAbilities({ action: Action.Read, subject: Cv })
  getMemberCvs(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return this.cvsService.getMemberCvs(memberId, user);
  }

  @Post('regenerate/:id')
  @CheckAbilities({ action: Action.Update, subject: Cv })
  regenerateCv(
    @AuthUser() user: AuthUserDto,
    @Param('id') id: string,
    @Body() dto: RegenerateCvDto,
  ) {
    return this.cvsService.regenerateCv(id, dto.query, user, dto.messages);
  }

  @Get()
  @CheckAbilities({ action: Action.Read, subject: Cv })
  getCvs(@AuthUser() user: AuthUserDto, @Query() query: GetCvsDto) {
    return this.cvsService.getCvs(query, user);
  }

  @Get('getMemberBase64Avatar/:memberId')
  @CheckAbilities({ action: Action.Read, subject: Member })
  getMemberBase64Avatar(
    @AuthUser() user: AuthUserDto,
    @Param('memberId') memberId: string,
  ) {
    return this.cvsService.getMemberBase64Avatar(memberId, user);
  }
}
