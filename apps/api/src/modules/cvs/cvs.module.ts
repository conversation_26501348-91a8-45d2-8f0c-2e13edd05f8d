import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { Cv, CvSchema } from './cv.schema';
import { CvsController } from './cvs.controller';
import { CvsService } from './cvs.service';
import { AiModule } from '../ai/ai.module';
import { CaslModule } from '../casl/casl.module';
import { Customer, CustomerSchema } from '../customers/customer.schema';
import { Member, MemberSchema } from '../members/member.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Cv.name, schema: CvSchema },
      { name: Member.name, schema: MemberSchema },
      { name: Customer.name, schema: CustomerSchema },
    ]),
    CaslModule,
    AiModule,
  ],
  controllers: [CvsController],
  providers: [CvsService],
  exports: [CvsService],
})
export class CvsModule {}
