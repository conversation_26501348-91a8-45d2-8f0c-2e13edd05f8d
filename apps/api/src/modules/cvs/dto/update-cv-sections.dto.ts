import { Type } from 'class-transformer';
import {
  IsNumber,
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
  IsBoolean,
  IsDate,
  IsNotEmpty,
} from 'class-validator';
import { Trim } from 'shared/utils/decorators';

export class CvSectionBaseDto {
  @IsNumber()
  order: number;

  @IsString()
  @IsNotEmpty()
  title: string;

  @IsBoolean()
  active: boolean;
}

export class CvSocialDto {
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  inputs: string[];

  @IsBoolean()
  active: boolean;
}

export class HidableInputDto {
  @Trim()
  @IsString()
  value: string;

  @IsBoolean()
  active: boolean;
}

export class PersonalInfoDataDto {
  @ValidateNested()
  @Type(() => HidableInputDto)
  firstName: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  lastName: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  jobTitle: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  location: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  nationality: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  email: HidableInputDto;

  @ValidateNested()
  @Type(() => HidableInputDto)
  telephone: HidableInputDto;

  @ValidateNested({ each: true })
  @Type(() => CvSocialDto)
  socials: CvSocialDto;
}

export class PersonalInfoDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => PersonalInfoDataDto)
  data: PersonalInfoDataDto;
}

export class AboutMeDataDto {
  @Trim()
  @IsString()
  description: string;
}

export class AboutMeDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => AboutMeDataDto)
  data!: AboutMeDataDto;
}

export class WorkHistoryDataItemDto {
  @IsBoolean()
  active: boolean;

  @Trim()
  @IsString()
  companyName: string;

  @Trim()
  @IsString()
  roleTitle: string;

  @Trim()
  @IsString()
  description: string;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  @IsBoolean()
  isCurrent: boolean;
}

export class WorkHistoryDto extends CvSectionBaseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => WorkHistoryDataItemDto)
  data: WorkHistoryDataItemDto[];
}

export class EducationRecordDto {
  @IsBoolean()
  active: boolean;

  @Trim()
  @IsString()
  schoolName: string;

  @Trim()
  @IsString()
  degree: string;

  @Trim()
  @IsString()
  description: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}

export class EducationDto extends CvSectionBaseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EducationRecordDto)
  data!: EducationRecordDto[];
}

export class CertificationsDataDto {
  @Trim()
  @IsString()
  description: string;
}

export class CertificationsDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => CertificationsDataDto)
  data: CertificationsDataDto;
}

export class SkillsDataDto {
  @Trim()
  @IsString()
  description: string;
}

export class SkillsDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => SkillsDataDto)
  data: SkillsDataDto;
}

export class LanguagesDataDto {
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  languages: string[];
}

export class LanguagesDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => LanguagesDataDto)
  data!: LanguagesDataDto;
}

export class CustomDataDto {
  @Trim()
  @IsString()
  description: string;
}

export class CustomDto extends CvSectionBaseDto {
  @ValidateNested()
  @Type(() => CustomDataDto)
  data: CustomDataDto;
}

export class UpdateCvSectionsDto {
  @ValidateNested()
  @Type(() => PersonalInfoDto)
  personalInfo: PersonalInfoDto;

  @ValidateNested()
  @Type(() => AboutMeDto)
  aboutMe: AboutMeDto;

  @ValidateNested()
  @Type(() => WorkHistoryDto)
  workHistory: WorkHistoryDto;

  @ValidateNested()
  @Type(() => EducationDto)
  education: EducationDto;

  @ValidateNested()
  @Type(() => CertificationsDto)
  certifications: CertificationsDto;

  @ValidateNested()
  @Type(() => SkillsDto)
  skills: SkillsDto;

  @ValidateNested()
  @Type(() => LanguagesDto)
  languages: LanguagesDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomDto)
  customSections: CustomDto[];
}
