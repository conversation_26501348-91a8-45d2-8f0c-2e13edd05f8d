import { Type } from 'class-transformer';
import { IsOptional, IsEnum, ValidateNested } from 'class-validator';
import { UpdateCvInput } from 'shared/inputs';
import { CvStatus, Template } from 'shared/types';

import { CvPreferencesDto } from './create-cv.dto';

export class UpdateCvDto implements UpdateCvInput {
  @IsOptional()
  @ValidateNested()
  @Type(() => CvPreferencesDto)
  preferences?: CvPreferencesDto;

  @IsOptional()
  @IsEnum(CvStatus, { message: 'Unknown CV status' })
  status?: CvStatus;

  @IsOptional()
  @IsEnum(Template, { message: 'Unknown template' })
  template?: Template;
}
