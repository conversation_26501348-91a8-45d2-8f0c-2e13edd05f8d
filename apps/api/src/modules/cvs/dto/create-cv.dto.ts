import { Type } from 'class-transformer';
import {
  IsNumber,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsArray,
  IsBoolean,
  IsEnum,
  IsDate,
  ValidateNested,
  IsMongoId,
} from 'class-validator';
import { CvPreferencesInput } from 'shared/inputs';
import { <PERSON><PERSON>rency, TimeRange } from 'shared/types';
import { Trim } from 'shared/utils/decorators';

export class CostRateDto {
  @IsEnum(Currency, { message: 'Unknown currency' })
  currency: Currency;

  @IsNumber()
  amount: number;

  @IsEnum(TimeRange, { message: 'Unknown time range' })
  timeRange: TimeRange;
}

export class CvPreferencesDto implements CvPreferencesInput {
  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'CV Alias cannot be empty. ' })
  title: string;

  @IsOptional()
  @IsNumber()
  maxPages?: number;

  @IsOptional()
  @IsString()
  role?: string;

  @IsOptional()
  @IsString()
  level?: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => CostRateDto)
  costRate?: CostRateDto;

  @IsOptional()
  @IsMongoId({ message: 'Invalid customer ID' })
  customer?: string;

  @IsOptional()
  @IsString()
  link?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  contractStart?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  contractEnd?: Date;

  @IsOptional()
  @IsBoolean()
  autoRenewal?: boolean;

  @IsOptional()
  @IsNumber()
  leastExperience?: number;

  @IsOptional()
  @IsNumber()
  maxExperience?: number;

  @IsOptional()
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  skills?: string[];

  @IsOptional()
  @IsString()
  description?: string;
}
