import { ForbiddenError } from '@casl/ability';
import {
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { escapeRegExp } from 'lodash';
import { Model, Types } from 'mongoose';
import { SortOrder } from 'shared/dto/common.dto';
import { GetMembersSortBy } from 'shared/dto/members/get-members.dto';

import { CreateMemberDto, GetMembersDto } from './dto';
import { UpdateMemberDto } from './dto/update-member.dto';
import { Member, MemberDocument } from './member.schema';
import { CaslAbilityFactory } from '../casl/casl-ability.factory';
import { Action } from '../casl/casl.types';
import { ICv } from '../cvs/cv.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { S3Provider } from '../global/s3.provider';

@Injectable()
export class MembersService {
  private readonly logger = new Logger(MembersService.name);
  constructor(
    @InjectModel(Member.name)
    private memberModel: Model<MemberDocument>,
    private caslAbilityFactory: CaslAbilityFactory,
  ) {}

  async createMember(dto: CreateMemberDto, orgId: string | Types.ObjectId) {
    return await this.memberModel.create({
      ...dto,
      organization: orgId,
    });
  }

  async updateMember(
    memberId: string | Types.ObjectId,
    dto: UpdateMemberDto,
    authUser: AuthUserDto,
  ) {
    await this.findMemberAndAuthorize(memberId, authUser, Action.Update);

    return await this.memberModel.findByIdAndUpdate(memberId, { $set: dto });
  }

  async getMemberById(
    memberId: string | Types.ObjectId,
    authUser: AuthUserDto,
  ) {
    await this.findMemberAndAuthorize(memberId, authUser, Action.Read);

    return await this.memberModel.findById(memberId);
  }

  async getOrgMembers(
    orgId: string | Types.ObjectId,
    { source, search, page, itemsPerPage, sortBy, sortOrder }: GetMembersDto,
  ) {
    // Create a filter object with organization ID
    const filter: Record<string, unknown> = { organization: orgId };

    // Add source filter if provided
    if (source) {
      filter.source = source;
    }

    // Add search filter if provided
    if (search) {
      const escapedSearch = escapeRegExp(search);
      filter.$or = [
        { firstName: { $regex: escapedSearch, $options: 'i' } },
        { lastName: { $regex: escapedSearch, $options: 'i' } },
        { email: { $regex: escapedSearch, $options: 'i' } },
        { currentPosition: { $regex: escapedSearch, $options: 'i' } },
      ];
    }

    // Build sort object
    const sortObject: Record<string, 1 | -1> = {};
    const sortDirection = sortOrder === SortOrder.ASC ? 1 : -1;

    switch (sortBy) {
      case GetMembersSortBy.NAME:
        sortObject.firstName = sortDirection;
        sortObject.lastName = sortDirection;
        break;
      case GetMembersSortBy.CREATED_AT:
        sortObject.createdAt = sortDirection;
        break;
      case GetMembersSortBy.UPDATED_AT:
        sortObject.updatedAt = sortDirection;
        break;
      default:
        sortObject.firstName = 1;
        sortObject.lastName = 1;
    }

    const members = await this.memberModel
      .find(filter)
      .sort(sortObject)
      .skip((page - 1) * itemsPerPage)
      .limit(itemsPerPage)
      .populate<{ cvs: ICv[] }>({
        path: 'cvs',
        populate: {
          path: 'preferences.customer',
        },
      });

    const totalMembers = await this.memberModel.countDocuments(filter);

    return { members, totalMembers };
  }

  async uploadMemberAvatar(
    memberId: string,
    authUser: AuthUserDto,
    avatar?: Express.Multer.File,
  ) {
    if (!avatar) {
      throw new Error('File is not passed');
    }
    await this.findMemberAndAuthorize(memberId, authUser, Action.Update);

    const sendData = await new S3Provider().save(
      avatar.buffer,
      avatar.mimetype,
      `member/${memberId}/avatar/${avatar.originalname}`,
    );

    await this.memberModel.findByIdAndUpdate(memberId, {
      avatar: sendData.Location,
    });
  }

  async removeMemberAvatar(memberId: string, authUser: AuthUserDto) {
    const member = await this.findMemberAndAuthorize(
      memberId,
      authUser,
      Action.Update,
    );

    const memberAvatar = member.avatar;

    if (memberAvatar) {
      const avatarKey = memberAvatar.replace(
        new RegExp('^' + process.env.AWS_BUCKET_URL + '/'),
        '',
      );
      await new S3Provider().delete(avatarKey);

      await this.memberModel.findByIdAndUpdate(memberId, {
        $unset: { avatar: '' },
      });
    }
  }

  private async findMemberAndAuthorize(
    memberId: string | Types.ObjectId,
    authUser: AuthUserDto,
    action: Action,
  ): Promise<MemberDocument> {
    const member = await this.memberModel.findById(memberId);
    if (!member) {
      throw new NotFoundException(`Member with ID "${memberId}" not found`);
    }

    const ability = this.caslAbilityFactory.createForUser(authUser);

    const memberForCheck = {
      ...member.toObject(),
      constructor: Member,
      organization: member.organization.toString(),
    } as unknown as Member & { organization: string };

    try {
      ForbiddenError.from(ability).throwUnlessCan(action, memberForCheck);
    } catch (error) {
      if (error instanceof ForbiddenError) {
        throw new ForbiddenException(error.message);
      }
      throw error;
    }

    return member;
  }
}
