import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose, { Document, Types } from 'mongoose';
import {
  Currency,
  ExpertiseLevelEnum,
  MemberSource,
  TimeRange,
  UserType,
} from 'shared/types';

import { Cv } from '../../modules/cvs/cv.schema';
import { Organization } from '../organization/organization.schema';

export interface ICostRate {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

@Schema()
export class MemberEducation {
  @Prop({ type: String, required: true })
  schoolName: string;

  @Prop({ type: String })
  degree: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: Date })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;
}

@Schema()
export class MemberExperience {
  @Prop({ type: String, required: true })
  companyName: string;

  @Prop({ type: String })
  roleTitle: string;

  @Prop({ type: String })
  description: string;

  @Prop({ type: Date, required: true })
  startDate: Date;

  @Prop({ type: Date })
  endDate: Date;

  @Prop({ type: Boolean })
  isCurrent: boolean;
}

@Schema({ timestamps: true })
export class MemberSkill {
  @Prop({ type: String, required: true })
  msId: string;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, enum: ExpertiseLevelEnum, required: true })
  level: ExpertiseLevelEnum;

  @Prop({ type: Boolean, required: true })
  isSoftware: boolean;
}

@Schema({ timestamps: true })
export class MemberCertification {
  @Prop({ type: String, required: true })
  msId: string;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, required: true })
  organization: string;
}

@Schema({ timestamps: true })
export class Member {
  _id: Types.ObjectId;

  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Organization',
    required: true,
  })
  organization: Types.ObjectId | Organization;

  @Prop({ type: String })
  avatar?: string;

  @Prop({ type: String, required: true })
  firstName: string;

  @Prop({ type: String })
  lastName?: string;

  @Prop({ type: String })
  email?: string;

  @Prop({ type: String })
  location?: string;

  @Prop({ type: String })
  telephone?: string;

  @Prop({ type: String })
  currentPosition?: string;

  @Prop({ type: String })
  currentLevel?: string;

  @Prop({ type: Number })
  yearsOfExperience?: number;

  @Prop({ type: [String], default: [] })
  languages: string[];

  @Prop({ type: String, enum: UserType })
  type?: UserType;

  @Prop({ type: [String], default: [] })
  clients: string[];

  @Prop({
    type: {
      currency: { type: String, enum: Currency },
      amount: Number,
      timeRange: { type: String, enum: TimeRange },
    },
  })
  costRate?: ICostRate;

  @Prop({
    type: {
      currency: { type: String, enum: Currency },
      amount: Number,
      timeRange: { type: String, enum: TimeRange },
    },
  })
  costToCompany?: ICostRate;

  @Prop({ type: [String], default: [] })
  socials: string[];

  @Prop({ type: String, enum: MemberSource, required: true })
  source: MemberSource;

  @Prop({ type: String })
  sourceId?: string;

  //FIXME: this field is not needed if member has a ist of competences
  @Prop({ type: Number })
  competencesAmount?: number;

  @Prop({ type: [mongoose.Schema.Types.ObjectId], ref: 'Cv', default: [] })
  cvs: Types.ObjectId[] | Cv[];

  @Prop({ type: [MemberExperience], default: [] })
  workExperience: MemberExperience[];

  @Prop({ type: [MemberEducation], default: [] })
  education: MemberEducation[];

  @Prop({ type: [MemberCertification], default: [] })
  certifications: MemberCertification[];

  @Prop({ type: [MemberSkill], default: [] })
  skills: MemberSkill[];
}

export type MemberDocument = Member & Document;
export const MemberSchema = SchemaFactory.createForClass(Member);
