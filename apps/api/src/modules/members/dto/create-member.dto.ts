import { IsOptional, IsString, IsEnum } from 'class-validator';
import { CreateMemberInput } from 'shared/inputs';
import { MemberSource } from 'shared/types';

import { UpdateMemberDto } from './update-member.dto';

export class CreateMemberDto
  extends UpdateMemberDto
  implements CreateMemberInput
{
  @IsEnum(MemberSource, { message: 'Unknown source' })
  source: MemberSource;

  @IsOptional()
  @IsString()
  sourceId?: string;
}
