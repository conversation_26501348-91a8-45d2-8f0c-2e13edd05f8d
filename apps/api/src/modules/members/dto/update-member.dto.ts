import { Transform, Type } from 'class-transformer';
import {
  IsAlphanumeric,
  IsArray,
  IsBoolean,
  IsDate,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MinLength,
  ValidateNested,
} from 'class-validator';
import { UpdateMemberInput } from 'shared/inputs';
import {
  UserType,
  Currency,
  TimeRange,
  ExpertiseLevelEnum,
} from 'shared/types';
import { Trim } from 'shared/utils/decorators';

export class CostRateDto {
  @IsEnum(Currency, { message: 'Unknown currency' })
  currency: Currency;

  @IsNumber()
  amount: number;

  @IsEnum(TimeRange, { message: 'Unknown time range' })
  timeRange: TimeRange;
}

export class MemberEducationRecordDto {
  @IsOptional()
  @IsBoolean()
  hidden?: boolean;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'School name cannot be empty. ' })
  schoolName: string;

  @Trim()
  @IsOptional()
  @IsString()
  degree?: string;

  @Trim()
  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;
}

export class MemberWorkHistoryRecordDto {
  @IsOptional()
  @IsBoolean()
  hidden?: boolean;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'Company name cannot be empty. ' })
  companyName: string;

  @Trim()
  @IsOptional()
  @IsString()
  roleTitle?: string;

  @Trim()
  @IsOptional()
  @IsString()
  description?: string;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @IsOptional()
  @Type(() => Date)
  @IsDate()
  endDate?: Date;

  @IsOptional()
  @IsBoolean()
  isCurrent?: boolean;
}

export class MemberCertification {
  @IsString()
  msId: string;

  @IsString()
  name: string;

  @IsString()
  organization: string;
}

export class MemberSkill {
  @IsString()
  msId: string;

  @IsString()
  name: string;

  @IsEnum(ExpertiseLevelEnum, { message: 'Unknown expertise' })
  level: ExpertiseLevelEnum;

  @IsBoolean()
  isSoftware: boolean;
}

export class UpdateMemberDto implements UpdateMemberInput {
  @IsOptional()
  @IsString()
  avatar?: string;

  @Trim()
  @IsString()
  @IsNotEmpty({ message: 'First name cannot be empty. ' })
  @MinLength(3, { message: 'First name must be at least 3 characters long' })
  @IsAlphanumeric(undefined, {
    message: 'First name must not contain symbols',
  })
  firstName: string;

  @Trim()
  @IsOptional()
  @IsString()
  lastName?: string;

  @Transform(({ value }) => (value === '' ? undefined : value))
  @Trim()
  @IsOptional()
  @IsEmail({}, { message: 'Invalid email format. ' })
  email?: string;

  @Trim()
  @IsOptional()
  @IsString()
  location?: string;

  @Trim()
  @IsOptional()
  @IsString()
  telephone?: string;

  @Trim()
  @IsOptional()
  @IsString()
  currentPosition?: string;

  @IsOptional()
  @IsString()
  currentLevel?: string;

  @IsOptional()
  @IsNumber()
  yearsOfExperience?: number;

  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  languages?: string[];

  @IsOptional()
  @IsEnum(UserType, { message: 'Unknown user type' })
  type?: UserType;

  @IsOptional()
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  clients?: string[];

  @IsOptional()
  @ValidateNested()
  @Type(() => CostRateDto)
  costRate?: CostRateDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => CostRateDto)
  costToCompany?: CostRateDto;

  @IsOptional()
  @Trim({ each: true })
  @IsArray()
  @IsString({ each: true })
  socials?: string[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MemberWorkHistoryRecordDto)
  workExperience?: MemberWorkHistoryRecordDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MemberEducationRecordDto)
  education?: MemberEducationRecordDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MemberCertification)
  certifications?: MemberCertification[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MemberSkill)
  skills?: MemberSkill[];
}
