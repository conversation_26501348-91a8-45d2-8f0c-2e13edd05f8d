import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TokenDocument = Token & Document;

@Schema({ timestamps: true })
export class Token {
  createdAt: Date;

  @Prop({ required: true, type: String, index: true })
  token: string;

  @Prop({ type: String })
  email: string;

  @Prop({ type: String })
  password: string;

  @Prop({ type: Object })
  metadata?: Record<string, unknown>;
}

export const TokenSchema = SchemaFactory.createForClass(Token);
