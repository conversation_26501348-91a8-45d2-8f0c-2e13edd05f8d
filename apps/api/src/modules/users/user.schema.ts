import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema, Types } from 'mongoose';
import { UserRole } from 'shared/types';

export type UserDocument = User & Document;

export enum UserStatus {
  active = 1,
  inactive = 0,
}

@Schema({ _id: false })
export class OrganizationMembership {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Organization' })
  orgId: Types.ObjectId;

  @Prop({
    required: true,
    enum: UserRole,
    type: String,
  })
  role: UserRole;

  @Prop({
    type: Date,
    default: Date.now,
  })
  joinedDate: Date;
}

export const OrganizationMembershipSchema = SchemaFactory.createForClass(
  OrganizationMembership,
);

@Schema({ timestamps: true })
export class User {
  _id: Types.ObjectId;

  @Prop({
    required: true,
    type: String,
    unique: true,
  })
  email: string;

  @Prop({
    required: true,
    type: String,
  })
  firstName: string;

  @Prop({
    required: true,
    type: String,
  })
  lastName: string;

  @Prop({
    type: String,
    required: false, // Or true if an avatar is mandatory, but usually optional
  })
  avatar?: string;

  @Prop({
    type: String,
    select: false,
  })
  password: string;

  @Prop({
    required: true,
    default: UserStatus.inactive,
    enum: UserStatus,
    type: Number,
  })
  status: number;

  @Prop({ type: [OrganizationMembershipSchema], default: [] })
  availableOrganizations: OrganizationMembership[];

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Organization' })
  organization: Types.ObjectId;

  @Prop({ type: Date, select: false })
  deletionScheduledDate?: Date;
}

export const UserSchema = SchemaFactory.createForClass(User);
