import {
  Body,
  Controller,
  Get,
  Post,
  Delete,
  UseGuards,
  Param,
} from '@nestjs/common';

import { AcceptInviteDto } from './dto/accept-invite.dto';
import { CreateInviteDto } from './dto/create-invite.dto';
import { InviteService } from './invite.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AllowAnonymous } from '../global/decorators/allow-anonymous.decorator';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { CheckPlanLimit } from '../stripe/decorators/check-plan-limit.decorator';
import { LimitType, PlanLimitGuard } from '../stripe/guards/plan-limit.guard';

@Controller('invites')
export class InviteController {
  constructor(private readonly inviteService: InviteService) {}

  @UseGuards(AuthGuard, PlanLimitGuard)
  @CheckPlanLimit(LimitType.USERS)
  @Post()
  async createOrganizationInvite(
    @Body() createInviteDto: CreateInviteDto,
    @AuthUser() user: AuthUserDto,
  ) {
    const createdInvite = await this.inviteService.createOrganizationInvite(
      createInviteDto,
      user,
    );
    return createdInvite;
  }

  @UseGuards(AuthGuard)
  @Get()
  async getOrganizationInvites(@AuthUser() user: AuthUserDto) {
    const organizationInvites =
      await this.inviteService.getOrganizationInvites(user);
    return organizationInvites;
  }

  @UseGuards(AuthGuard)
  @Post('accept')
  async acceptInvite(
    @Body() acceptInviteDto: AcceptInviteDto,
    @AuthUser() user: AuthUserDto,
  ) {
    return this.inviteService.acceptInvite(acceptInviteDto.token, user);
  }

  @UseGuards(AuthGuard)
  @AllowAnonymous()
  @Get('details/:token')
  async getInviteDetails(
    @Param('token') token: string,
    @AuthUser() user: AuthUserDto,
  ) {
    const result = await this.inviteService.getInviteDetails(token);
    return {
      ...result,
      isLoggedIn: !!user,
    };
  }

  @UseGuards(AuthGuard)
  @Delete(':id')
  async deleteOrganizationInvite(
    @Param('id') inviteId: string,
    @AuthUser() user: AuthUserDto,
  ) {
    return this.inviteService.deleteOrganizationInvite(inviteId, user);
  }
}
