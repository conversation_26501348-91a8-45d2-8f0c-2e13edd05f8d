import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { isPast } from 'date-fns';
import { Model, Types } from 'mongoose';
import { InviteStatus } from 'shared/types';

import { CreateInviteDto } from './dto/create-invite.dto';
import { Invite, InviteDocument } from './invite.schema';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { generateShortToken } from '../global/helpers';
import { MailService } from '../mail/mail.service';
import { OrganizationService } from '../organization/organization.service';

const INVITE_EXPIRATION_TIME_MS = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds

const INVITE_ERRORS = {
  NOT_FOUND: 'Invite not found',
  EXPIRED: 'Invite expired',
  ALREADY_ACCEPTED: 'Invite already accepted',
  EMAIL_MISMATCH: "You can't accept this invite",
} as const;

@Injectable()
export class InviteService {
  constructor(
    @InjectModel(Invite.name) private inviteModel: Model<InviteDocument>,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
    private readonly mailService: MailService,
  ) {}

  public async createOrganizationInvite(
    createInviteDto: CreateInviteDto,
    user: AuthUserDto,
  ) {
    // Check if organization is scheduled for deletion
    const organization = await this.organizationService.findById(
      user.organization._id,
    );
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (organization.deletionScheduledDate) {
      throw new BadRequestException(
        'Cannot send invites for an organization scheduled for deletion',
      );
    }

    // Check if an invite already exists for this email and organization
    const existingInvite = await this.inviteModel.findOne({
      email: createInviteDto.email,
      organization: user.organization._id,
    });

    let invite: InviteDocument;

    if (existingInvite) {
      // Update the existing invite's expiration date and role
      existingInvite.expiresAt = new Date(
        Date.now() + INVITE_EXPIRATION_TIME_MS,
      );
      existingInvite.role = createInviteDto.role;
      invite = await existingInvite.save();
    } else {
      // Create a new invite if one doesn't exist
      const token = await this.generateInviteToken();
      invite = await new this.inviteModel({
        token,
        invitedBy: user._id,
        email: createInviteDto.email,
        role: createInviteDto.role,
        expiresAt: new Date(Date.now() + INVITE_EXPIRATION_TIME_MS),
        organization: user.organization._id,
      }).save();
    }

    // Send invite email with token, email, and organization name
    await this.mailService.sendInviteEmail(
      invite.email,
      invite.token,
      user.organization.name,
    );

    return invite;
  }

  public async getOrganizationInvites(user: AuthUserDto) {
    const organizationInvites = await this.inviteModel.find({
      organization: user.organization._id,
    });

    // Check and update expired invites
    const updatedInvites = await Promise.all(
      organizationInvites.map(async (invite) => {
        if (
          invite.status === InviteStatus.PENDING &&
          isPast(invite.expiresAt)
        ) {
          invite.status = InviteStatus.EXPIRED;
          await invite.save();
        }
        return invite;
      }),
    );

    return updatedInvites;
  }

  public async deleteOrganizationInvite(inviteId: string, user: AuthUserDto) {
    const invite = await this.inviteModel.findOne({
      _id: inviteId,
      organization: user.organization._id,
    });

    if (!invite) {
      throw new NotFoundException('Invite not found');
    }

    await this.inviteModel.deleteOne({ _id: inviteId });
    return { message: 'Invite deleted successfully' };
  }

  private async generateInviteToken() {
    let token: string;
    do {
      token = generateShortToken();
    } while (await this.inviteModel.exists({ token }));
    return token;
  }

  public async deleteOrganizationInvites(
    organizationId: string | Types.ObjectId,
  ) {
    await this.inviteModel.deleteMany({ organization: organizationId });
  }

  public async acceptInvite(
    token: string,
    user: Pick<AuthUserDto, 'email' | '_id'>,
  ) {
    const invite = await this.inviteModel.findOne({
      token,
    });

    if (!invite) {
      throw new NotFoundException(INVITE_ERRORS.NOT_FOUND);
    }
    if (isPast(invite.expiresAt)) {
      // set status to expired
      invite.status = InviteStatus.EXPIRED;
      await invite.save();
      throw new BadRequestException(INVITE_ERRORS.EXPIRED);
    }
    if (invite.email !== user.email) {
      console.error('Email mismatch', {
        inviteEmail: invite.email,
        userEmail: user.email,
      });
      throw new BadRequestException(INVITE_ERRORS.EMAIL_MISMATCH);
    }
    if (invite.status === InviteStatus.ACCEPTED) {
      throw new ConflictException(INVITE_ERRORS.ALREADY_ACCEPTED);
    }

    // Check if organization is scheduled for deletion
    const organization = await this.organizationService.findById(
      invite.organization,
    );
    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    if (organization.deletionScheduledDate) {
      throw new BadRequestException(
        'Cannot accept invite for an organization scheduled for deletion',
      );
    }

    await this.organizationService.addUserToOrganization(
      invite.organization,
      user._id,
      invite.role,
    );
    invite.status = InviteStatus.ACCEPTED;
    await invite.save();

    return invite;
  }

  public async getInviteDetails(token: string) {
    const invite = await this.inviteModel.findOne({ token });
    if (!invite) {
      throw new NotFoundException('Invite not found');
    }
    const isExpired = isPast(invite.expiresAt);
    if (isExpired) {
      invite.status = InviteStatus.EXPIRED;
      await invite.save();
    }
    const organization = await this.organizationService.findById(
      invite.organization,
    );
    const organizationName = organization ? organization.name : '';
    // Check if user exists by email
    const userExists =
      !!(await this.organizationService.usersService.findByEmail(invite.email));
    return {
      inviteToken: invite.token,
      organizationName,
      status: invite.status,
      isExpired,
      userExists,
    };
  }

  public async validateInviteDuringSignup(token: string, email: string) {
    const invite = await this.inviteModel.findOne({ token });
    if (!invite) {
      throw new BadRequestException('Invite not found');
    }
    if (invite.email !== email) {
      throw new BadRequestException(
        'Invite email does not match sign up email',
      );
    }
    return invite;
  }
}
