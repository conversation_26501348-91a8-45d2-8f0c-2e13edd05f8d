import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { CvProfileFilter } from 'shared/types';

import { MuchskillsService } from './muchskills.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';

@Controller('muchskills')
export class MuchskillsController {
  constructor(private readonly muchskillsService: MuchskillsService) {}

  @UseGuards(AuthGuard)
  @Post('members-sync/:orgId')
  async syncProfiles(@Param('orgId') orgId: string) {
    return this.muchskillsService.syncProfiles(orgId);
  }

  @UseGuards(AuthGuard)
  @Post('members/:orgId')
  async getProfiles(
    @Param('orgId') orgId: string,
    @Body()
    {
      page,
      itemsPerPage,
      searchValue,
      filter,
    }: {
      page: number;
      itemsPerPage: number;
      searchValue: string;
      filter: CvProfileFilter;
    },
  ) {
    return this.muchskillsService.getProfiles(
      orgId,
      page,
      itemsPerPage,
      searchValue,
      filter,
    );
  }

  @UseGuards(AuthGuard)
  @Post('create-member/:orgId/:email')
  async createMemberFromMs(
    @Param('orgId') orgId: string,
    @Param('email') email: string,
  ) {
    return this.muchskillsService.createMemberFromMs(orgId, email);
  }

  @Post('skills/search')
  async getSkills(
    @AuthUser() user: AuthUserDto,
    @Body()
    {
      searchValue,
      orgId,
      skillsToExclude,
    }: {
      searchValue: string;
      orgId: string;
      skillsToExclude?: string[];
    },
  ) {
    return await this.muchskillsService.getSkills(
      orgId,
      searchValue,
      skillsToExclude,
    );
  }

  @Post('certificates/search')
  async getCertificates(
    @Body()
    {
      searchValue,
      certificatesToExclude,
    }: {
      searchValue: string;
      certificatesToExclude?: string[];
    },
  ) {
    return await this.muchskillsService.getCertificates(
      searchValue,
      certificatesToExclude,
    );
  }
}
