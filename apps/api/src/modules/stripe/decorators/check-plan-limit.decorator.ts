import { SetMetadata } from '@nestjs/common';

import { LIMIT_TYPE_KEY, LimitType } from '../guards/plan-limit.guard'; // Assumes LimitType and KEY are in plan-limit.guard.ts

/**
 * Decorator to specify which type of plan limit to check for a route handler.
 * To be used in conjunction with PlanLimitGuard.
 * @param limitType - The type of limit to check (e.g., CVS, PROFILES).
 */
export const CheckPlanLimit = (limitType: LimitType) =>
  SetMetadata(LIMIT_TYPE_KEY, limitType);
