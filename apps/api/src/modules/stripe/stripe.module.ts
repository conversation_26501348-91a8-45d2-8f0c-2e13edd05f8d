import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { PlanLimitGuard } from './guards/plan-limit.guard';
import { PlanLimitsService } from './plan-limits.service';
import { StripeWebhookService } from './stripe-webhook.service';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { Cv, CvSchema } from '../cvs/cv.schema';
import { Invite, InviteSchema } from '../invite/invite.schema';
import { Member, MemberSchema } from '../members/member.schema';
import { OrganizationModule } from '../organization/organization.module';
import { User, UserSchema } from '../users/user.schema';
import { UsersModule } from '../users/users.module';

@Global()
@Module({
  imports: [
    ConfigModule,
    OrganizationModule,
    UsersModule,
    MongooseModule.forFeature([{ name: Cv.name, schema: CvSchema }]),
    MongooseModule.forFeature([{ name: Member.name, schema: MemberSchema }]),
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    MongooseModule.forFeature([{ name: Invite.name, schema: InviteSchema }]),
  ],
  providers: [
    StripeService,
    StripeWebhookService,
    PlanLimitsService,
    PlanLimitGuard,
  ],
  controllers: [StripeController],
  exports: [StripeService, StripeWebhookService, PlanLimitsService],
})
export class StripeModule {}
