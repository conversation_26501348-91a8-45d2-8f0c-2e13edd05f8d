import { Injectable, Logger } from '@nestjs/common';
import { Organization, StripePlanMapping } from 'shared/types';
import Stripe from 'stripe';
import StripeNode from 'stripe';

// Define the DTO type for updates, excluding _id to prevent type conflicts with Mongoose's ObjectId
type StripeOrganizationUpdateDto = Partial<Omit<Organization, '_id'>>;
// Extended type that includes MongoDB operators like $unset
type StripeOrganizationUpdateWithOperatorsDto = StripeOrganizationUpdateDto & {
  $unset?: Record<string, number>;
};

import { StripeService } from './stripe.service';
import { OrganizationService } from '../organization/organization.service';

const STRIPE_WEBHOOK_HANDLED_EVENTS = [
  'checkout.session.completed',
  'customer.created',
  'customer.subscription.created',
  'customer.subscription.updated',
  'customer.subscription.deleted',
  'invoice.payment_succeeded',
  'invoice.payment_failed',
] as const;

@Injectable()
export class StripeWebhookService {
  private readonly logger = new Logger(StripeWebhookService.name);

  constructor(
    private readonly organizationService: OrganizationService,
    private readonly stripeService: StripeService,
  ) {}

  private async mapStripePriceToPlanTier(
    price: Stripe.Price | undefined,
  ): Promise<StripePlanMapping> {
    if (!price || !price.product) {
      this.logger.warn(
        'mapStripePriceToPlanTier: Price or price.product is undefined, defaulting to FREE.',
      );
      return StripePlanMapping.FREE;
    }

    try {
      let product: Stripe.Product;
      if (typeof price.product === 'string') {
        product = await this.stripeService
          .getStripeInstance()
          .products.retrieve(price.product);
      } else if (
        price.product &&
        'deleted' in price.product &&
        price.product.deleted
      ) {
        this.logger.warn(
          `mapStripePriceToPlanTier: Product ${price.product.id} has been deleted. Defaulting to FREE.`,
        );
        return StripePlanMapping.FREE;
      } else {
        // price.product is already an expanded Stripe.Product object and not deleted
        product = price.product as Stripe.Product; // Cast here after check
      }

      if (product && product.metadata && product.metadata.id) {
        const tierMetadata = product.metadata.id.toLowerCase();
        switch (tierMetadata) {
          case 'pro':
            return StripePlanMapping.PRO;
          case 'business':
            return StripePlanMapping.BUSINESS;
          case 'enterprise':
            return StripePlanMapping.ENTERPRISE;
          case 'free':
            return StripePlanMapping.FREE;
          default:
            this.logger.warn(
              `mapStripePriceToPlanTier: Unknown tier '${tierMetadata}' in product metadata for product ${product.id}. Defaulting to FREE.`,
            );
            return StripePlanMapping.FREE;
        }
      } else {
        this.logger.warn(
          `mapStripePriceToPlanTier: Product metadata or metadata.id is missing for product ${price.product}. Defaulting to FREE. Price ID: ${price.id}`,
        );
        return StripePlanMapping.FREE;
      }
    } catch (error) {
      this.logger.error(
        `mapStripePriceToPlanTier: Error retrieving product or processing metadata for price ${price.id}: ${error.message}`,
      );
      return StripePlanMapping.FREE;
    }
  }

  private safeCreateDateFromTimestamp(
    timestamp: number | null | undefined,
  ): Date | null {
    if (typeof timestamp === 'number' && isFinite(timestamp)) {
      return new Date(timestamp * 1000);
    }
    // Log only if it was an unexpected non-numeric, non-null, non-undefined value
    if (
      timestamp !== null &&
      timestamp !== undefined &&
      typeof timestamp !== 'number'
    ) {
      this.logger.warn(
        `Invalid or non-numeric timestamp received for date conversion: ${timestamp}`,
      );
    }
    return null;
  }

  async handleWebhookEvent(event: StripeNode.Event): Promise<void> {
    try {
      const handledEvents =
        STRIPE_WEBHOOK_HANDLED_EVENTS as readonly Stripe.Event['type'][];
      if (!handledEvents.includes(event.type)) {
        this.logger.warn(`Unhandled webhook event type: ${event.type}`);
        return;
      }

      switch (event.type as Stripe.Event['type']) {
        case 'checkout.session.completed':
          await this.handleCheckoutSessionCompleted(event);
          break;
        case 'customer.created':
          await this.handleCustomerCreated(event);
          break;
        case 'customer.subscription.created':
          await this.handleCustomerSubscriptionCreated(event);
          break;
        case 'customer.subscription.updated':
          await this.handleCustomerSubscriptionUpdated(event);
          break;
        case 'customer.subscription.deleted':
          await this.handleCustomerSubscriptionDeleted(event);
          break;
        case 'invoice.payment_succeeded':
          await this.handleInvoicePaymentSucceeded(event);
          break;
        case 'invoice.payment_failed':
          await this.handleInvoicePaymentFailed(event);
          break;
      }
    } catch (error) {
      this.logger.error(
        `Error processing webhook event ${event.id} (${event.type}): ${error.message}`,
        error.stack,
      );
    }
  }

  private async handleCheckoutSessionCompleted(
    event: StripeNode.Event,
  ): Promise<void> {
    const session = event.data.object as StripeNode.Checkout.Session;
    this.logger.log(
      `Checkout session completed: ${session.id}, sub: ${session.subscription}, customer: ${session.customer}`,
    );
    if (
      session.mode === 'subscription' &&
      session.subscription &&
      session.customer
    ) {
      const subscriptionId = session.subscription as string;
      const customerId = session.customer as string;
      const organizationId =
        session.metadata?.organizationId ||
        (session.client_reference_id as string);

      if (!organizationId) {
        this.logger.error(
          `checkout.session.completed: Missing organizationId in metadata for session ${session.id}`,
        );
        return;
      }

      const subscription: StripeNode.Subscription = await this.stripeService
        .getStripeInstance()
        .subscriptions.retrieve(subscriptionId);
      if (!subscription) {
        this.logger.error(
          `checkout.session.completed: Could not retrieve subscription ${subscriptionId}`,
        );
        return;
      }
      const org = await this.organizationService.findById(organizationId);
      if (org && !org.stripeCustomerId) {
        await this.organizationService.updateOrganization(organizationId, {
          stripeCustomerId: customerId,
        });
        this.logger.log(
          `Org ${organizationId} updated with stripeCustomerId ${customerId} from checkout.session.completed`,
        );
      }

      const updateDataCheckout: StripeOrganizationUpdateDto = {
        stripeSubscriptionId: subscription.id,
        planStatus: subscription.status as Organization['planStatus'],
        planId: subscription.items.data[0]?.price.id,
        currentPeriodEnds: this.safeCreateDateFromTimestamp(
          subscription.items.data[0].current_period_end,
        ),
        planTier: await this.mapStripePriceToPlanTier(
          subscription.items.data[0]?.price,
        ),
      };

      await this.organizationService.updateByStripeCustomerId(
        customerId,
        updateDataCheckout,
      );
      this.logger.log(
        `Organization (customer ${customerId}) updated from checkout.session.completed for sub ${subscription.id}`,
      );
    } else if (session.mode === 'setup') {
      this.logger.log(`Setup intent successful for session: ${session.id}`);
    }
  }

  private async handleCustomerCreated(event: StripeNode.Event): Promise<void> {
    const customer = event.data.object as StripeNode.Customer;
    const organizationId = customer.metadata?.organizationId;
    if (organizationId && customer.id) {
      await this.organizationService.updateOrganization(organizationId, {
        stripeCustomerId: customer.id,
      });
      this.logger.log(
        `Organization ${organizationId} updated with stripeCustomerId ${customer.id} from customer.created`,
      );
    } else {
      this.logger.warn(
        `customer.created: Missing organizationId or customer.id in event. Customer ID: ${customer.id}, Org ID in metadata: ${organizationId}`,
      );
    }
  }

  private async handleCustomerSubscriptionCreated(
    event: StripeNode.Event,
  ): Promise<void> {
    const subscription = event.data.object as StripeNode.Subscription;
    const customerId = subscription.customer as string;
    this.logger.log(
      `Subscription created: ${subscription.id} for customer ${customerId}`,
    );

    const updateDataSubCreated: StripeOrganizationUpdateDto = {
      stripeSubscriptionId: subscription.id,
      planStatus: subscription.status as Organization['planStatus'],
      planId: subscription.items.data[0]?.price.id,
      currentPeriodEnds: this.safeCreateDateFromTimestamp(
        subscription.items.data[0].current_period_end,
      ),
      planTier: await this.mapStripePriceToPlanTier(
        subscription.items.data[0]?.price,
      ),
    };

    await this.organizationService.updateByStripeCustomerId(
      customerId,
      updateDataSubCreated,
    );
    this.logger.log(
      `Organization (customer ${customerId}) updated from customer.subscription.created for sub ${subscription.id}`,
    );
  }

  private async handleCustomerSubscriptionUpdated(
    event: StripeNode.Event,
  ): Promise<void> {
    const subscription = event.data.object as StripeNode.Subscription;
    const customerId = subscription.customer as string;

    this.logger.log(
      `Subscription updated: ${subscription.id}, status: ${subscription.status} for customer ${customerId}`,
    );
    const updateDataSubUpdated: StripeOrganizationUpdateDto = {
      stripeSubscriptionId: subscription.id,
      planStatus: subscription.status as Organization['planStatus'],
      planId: subscription.items.data[0]?.price.id,
      currentPeriodEnds: this.safeCreateDateFromTimestamp(
        subscription.items.data[0].current_period_end,
      ),
      planTier: await this.mapStripePriceToPlanTier(
        subscription.items.data[0]?.price,
      ),
    };

    await this.organizationService.updateByStripeCustomerId(
      customerId,
      updateDataSubUpdated,
    );
    this.logger.log(
      `Organization (customer: ${customerId}) updated from customer.subscription.updated for sub ${subscription.id}`,
    );
  }

  private async handleCustomerSubscriptionDeleted(
    event: StripeNode.Event,
  ): Promise<void> {
    const subscription = event.data.object as StripeNode.Subscription;
    const customerId = subscription.customer as string;

    this.logger.log(
      `Subscription deleted: ${subscription.id} for customer ${customerId}`,
    );

    const updateDataSubDeleted: StripeOrganizationUpdateWithOperatorsDto = {
      planStatus: 'canceled',
      planTier: StripePlanMapping.FREE,
      $unset: {
        stripeSubscriptionId: 1,
        planId: 1,
      },
    };

    // Try to get the most accurate end date
    const endedAt = this.safeCreateDateFromTimestamp(subscription.ended_at);
    const canceledAt = this.safeCreateDateFromTimestamp(
      subscription.canceled_at,
    );

    // Use the most accurate date available, with fallbacks
    if (endedAt) {
      updateDataSubDeleted.currentPeriodEnds = endedAt;
      this.logger.log(
        `Using subscription ended_at date: ${endedAt.toISOString()}`,
      );
    } else if (canceledAt) {
      updateDataSubDeleted.currentPeriodEnds = canceledAt;
      this.logger.log(
        `Using subscription canceled_at date: ${canceledAt.toISOString()}`,
      );
    } else {
      // Fallback: Current date when we processed this event
      const currentDate = new Date();
      updateDataSubDeleted.currentPeriodEnds = currentDate;
      this.logger.log(
        `Using current date for subscription end: ${currentDate.toISOString()}`,
      );
    }

    await this.organizationService.updateByStripeCustomerId(
      customerId,
      updateDataSubDeleted,
    );
    this.logger.log(
      `Organization (customer: ${customerId}) updated from customer.subscription.deleted for sub ${subscription.id}`,
    );
  }

  private async handleInvoicePaymentSucceeded(
    event: StripeNode.Event,
  ): Promise<void> {
    const invoice = event.data.object as StripeNode.Invoice;
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.parent.subscription_details
      .subscription as string;

    if (subscriptionId && customerId) {
      const subscriptionDetails: StripeNode.Subscription =
        await this.stripeService
          .getStripeInstance()
          .subscriptions.retrieve(subscriptionId);

      this.logger.log(
        `Invoice payment succeeded for subscription: ${subscriptionId} (customer ${customerId})`,
      );
      const updateDataInvoiceOk: StripeOrganizationUpdateDto = {
        planStatus: 'active',
        currentPeriodEnds: this.safeCreateDateFromTimestamp(
          subscriptionDetails.items.data[0].current_period_end,
        ),
      };

      await this.organizationService.updateByStripeCustomerId(
        customerId,
        updateDataInvoiceOk,
      );
      this.logger.log(
        `Organization (customer: ${customerId}) updated from invoice.payment_succeeded for sub ${subscriptionId}`,
      );
    }
  }

  private async handleInvoicePaymentFailed(
    event: StripeNode.Event,
  ): Promise<void> {
    const invoice = event.data.object as StripeNode.Invoice;
    const customerId = invoice.customer as string;
    const subscriptionId = invoice.parent.subscription_details
      .subscription as string;

    if (subscriptionId && customerId) {
      this.logger.warn(
        `Invoice payment failed for subscription: ${subscriptionId} (customer ${customerId})`,
      );
      await this.organizationService.updateByStripeCustomerId(customerId, {
        planStatus: 'past_due',
      });
      this.logger.log(
        `Organization (customer: ${customerId}) updated from invoice.payment_failed for sub ${subscriptionId}`,
      );
    }
  }
}
