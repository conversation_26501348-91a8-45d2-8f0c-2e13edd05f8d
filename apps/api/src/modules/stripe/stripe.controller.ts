import {
  Controller,
  Post,
  Body,
  Req,
  Res,
  HttpCode,
  HttpStatus,
  Logger,
  Inject,
  forwardRef,
  BadRequestException,
  UseGuards,
  Get,
  RawBodyRequest,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response } from 'express';
import StripeNode from 'stripe';

import { CreateCheckoutSessionDto } from './dto';
import { StripeWebhookService } from './stripe-webhook.service';
import { StripeService } from './stripe.service';
import { AuthGuard } from '../auth/guards/auth.guard';
import { AllowAnonymous } from '../global/decorators/allow-anonymous.decorator';
import { AuthUser } from '../global/decorators/user.decorator';
import { AuthUserDto } from '../global/dto/auth-user.dto';
import { OrganizationService } from '../organization/organization.service';

const STRIPE_API_BASE_PATH = 'stripe';

@Controller(STRIPE_API_BASE_PATH)
export class StripeController {
  private readonly logger = new Logger(StripeController.name);
  private readonly frontendUrl: string;
  private readonly defaultSuccessUrl: string;
  private readonly defaultCancelUrl: string;
  private readonly defaultPortalReturnUrl: string;

  constructor(
    private readonly stripeService: StripeService,
    private readonly configService: ConfigService,
    @Inject(forwardRef(() => OrganizationService))
    private readonly organizationService: OrganizationService,
    private readonly stripeWebhookService: StripeWebhookService,
  ) {
    this.frontendUrl =
      this.configService.get<string>('VITE_WEB_BASE_URL') ||
      'http://localhost:4200';
    this.defaultSuccessUrl = `${this.frontendUrl}/settings/billing`;
    this.defaultCancelUrl = `${this.frontendUrl}/settings/billing`;
    this.defaultPortalReturnUrl = `${this.frontendUrl}/account/billing`;
  }

  @UseGuards(AuthGuard)
  @Post('create-checkout-session')
  async createCheckoutSession(
    @Body() createCheckoutSessionDto: CreateCheckoutSessionDto,
    @AuthUser() authUser: AuthUserDto,
  ) {
    try {
      const successUrl =
        createCheckoutSessionDto.successUrl || this.defaultSuccessUrl;
      const cancelUrl =
        createCheckoutSessionDto.cancelUrl || this.defaultCancelUrl;
      const currency = createCheckoutSessionDto.currency;

      const session = await this.stripeService.initiateCheckoutForOrganization(
        authUser,
        createCheckoutSessionDto.priceId,
        successUrl,
        cancelUrl,
        currency,
      );

      return { sessionId: session.id, url: session.url };
    } catch (error) {
      if (error instanceof StripeNode.errors.StripeError) {
        throw new BadRequestException(`Stripe error: ${error.message}`);
      }
      throw error;
    }
  }

  @UseGuards(AuthGuard)
  @Post('create-portal-session')
  async createPortalSession(@AuthUser() authUser: AuthUserDto) {
    try {
      const portalSession =
        await this.stripeService.initiatePortalSessionForOrganization(authUser);

      return { url: portalSession.url };
    } catch (error) {
      if (error instanceof StripeNode.errors.StripeError) {
        throw new BadRequestException(`Stripe error: ${error.message}`);
      }
      // For other unhandled errors
      throw new BadRequestException(
        `Failed to create portal session: ${error.message}`,
      );
    }
  }

  @AllowAnonymous()
  @Post('webhooks')
  @HttpCode(HttpStatus.OK)
  async handleStripeWebhook(
    @Req() req: RawBodyRequest<Request>,
    @Res() res: Response,
  ) {
    const signature = req.headers['stripe-signature'] as string;
    const rawBody = req.rawBody;
    if (!signature) {
      this.logger.warn('Webhook received without stripe-signature header');
      return res
        .status(HttpStatus.BAD_REQUEST)
        .send('Missing stripe-signature header');
    }
    if (!rawBody) {
      this.logger.error(
        'Webhook raw body is undefined. Ensure RawBodyParser is configured correctly.',
      );
      return res
        .status(HttpStatus.INTERNAL_SERVER_ERROR)
        .send('Webhook processing error: raw body not available.');
    }

    let event: StripeNode.Event;

    try {
      event = this.stripeService.constructEventFromPayload(signature, rawBody);
      this.logger.log(`Webhook received: ${event.type}, id: ${event.id}`);
    } catch (err) {
      this.logger.error(
        `Webhook signature verification failed: ${err.message}`,
      );
      return res
        .status(HttpStatus.BAD_REQUEST)
        .send(`Webhook Error: ${err.message}`);
    }

    await this.stripeWebhookService.handleWebhookEvent(event);
    return res.status(HttpStatus.OK).send('Webhook received');
  }

  @UseGuards(AuthGuard)
  @Get('products')
  getCvProductsWithPrices() {
    return this.stripeService.getCvProductsWithPrices();
  }
}
