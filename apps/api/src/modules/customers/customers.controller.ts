import {
  Body,
  Controller,
  Post,
  Get,
  UseGuards,
  Param,
  Put,
  Delete,
} from '@nestjs/common';
import { AuthUser } from 'src/modules/global/decorators/user.decorator';
import { AuthUserDto } from 'src/modules/global/dto/auth-user.dto';

import { Customer } from './customer.schema';
import { CustomersService } from './customers.service';
import { CreateCustomerDto, UpdateCustomerDto } from './dto';
import { AuthGuard } from '../auth/guards/auth.guard';
import { Action } from '../casl/casl.types';
import { CheckAbilities } from '../casl/decorators/check-policies.decorator';
import { PoliciesGuard } from '../casl/guards/policies.guard';

@UseGuards(AuthGuard, PoliciesGuard)
@Controller('customers')
export class CustomersController {
  constructor(private customersService: CustomersService) {}

  @Post()
  @CheckAbilities({ action: Action.Create, subject: Customer })
  async createCustomer(
    @AuthUser() user: AuthUserDto,
    @Body() dto: CreateCustomerDto,
  ) {
    return await this.customersService.createCustomer(
      dto,
      user.organization._id,
    );
  }

  @Put(':customerId')
  @CheckAbilities({ action: Action.Update, subject: Customer })
  async updateCustomer(
    @AuthUser() user: AuthUserDto,
    @Param('customerId') customerId: string,
    @Body() dto: UpdateCustomerDto,
  ) {
    return await this.customersService.updateCustomer(customerId, dto, user);
  }

  @Get(':customerId')
  @CheckAbilities({ action: Action.Read, subject: Customer })
  async getCustomer(
    @AuthUser() user: AuthUserDto,
    @Param('customerId') customerId: string,
  ) {
    return await this.customersService.getCustomer(customerId, user);
  }

  @Get()
  @CheckAbilities({ action: Action.Read, subject: Customer })
  async getOrgCustomers(@AuthUser() user: AuthUserDto) {
    return await this.customersService.getOrgCustomers(user.organization._id);
  }

  @Delete(':customerId')
  @CheckAbilities({ action: Action.Delete, subject: Customer })
  async deleteCustomer(
    @AuthUser() user: AuthUserDto,
    @Param('customerId') customerId: string,
  ) {
    return await this.customersService.deleteCustomer(customerId, user);
  }
}
