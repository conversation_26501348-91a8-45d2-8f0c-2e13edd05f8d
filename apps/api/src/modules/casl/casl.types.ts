import { InferSubjects, Ability } from '@casl/ability';

import { Customer } from '../customers/customer.schema';
import { Cv } from '../cvs/cv.schema';
import { Member } from '../members/member.schema';
import { Organization } from '../organization/organization.schema';
import { User } from '../users/user.schema';

export enum Action {
  Manage = 'manage', // wildcard for any action
  Create = 'create',
  Read = 'read',
  Update = 'update',
  Delete = 'delete',
}

export type Subjects = InferSubjects<
  | typeof User
  | typeof Cv
  | typeof Member
  | typeof Customer
  | typeof Organization
  | 'all'
>;

export type AppAbility = Ability<[Action, Subjects]>;
