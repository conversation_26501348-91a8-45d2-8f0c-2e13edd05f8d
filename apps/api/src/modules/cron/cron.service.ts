import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { StripePlanMapping, StripePlanStatus } from 'shared/types';

import { OrganizationService } from '../organization/organization.service';
import { StripeService } from '../stripe/stripe.service';

@Injectable()
export class CronService {
  private readonly logger = new Logger(CronService.name);

  constructor(
    private readonly organizationService: OrganizationService,
    private readonly stripeService: StripeService,
  ) {}

  /**
   * Runs daily at midnight to check for and execute scheduled organization deletions
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  public async handleScheduledDeletions() {
    this.logger.log('Running scheduled organization deletions check');

    try {
      const result = await this.organizationService.executeScheduledDeletions();
      this.logger.log(
        `Deleted ${result.deletedCount} organizations that were scheduled for deletion`,
      );
    } catch (error) {
      this.logger.error(
        `Error executing scheduled deletions: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Runs daily at 2 AM to sync subscription statuses from Stripe
   * This ensures data consistency when webhooks fail during deployments or other issues
   */
  @Cron('0 2 * * *') // 2 AM daily
  public async syncStripeSubscriptions() {
    this.logger.log('Starting Stripe subscription sync');

    try {
      const result = await this.syncSubscriptionsFromStripe();
      this.logger.log(
        `Stripe sync completed: ${result.totalProcessed} organizations processed, ${result.updatedCount} updated, ${result.endedCount} subscriptions ended`,
      );
    } catch (error) {
      this.logger.error(
        `Error during Stripe subscription sync: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Syncs subscription data from Stripe for organizations with active subscriptions
   * Returns summary of sync results
   */
  public async syncSubscriptionsFromStripe(): Promise<{
    totalProcessed: number;
    updatedCount: number;
    endedCount: number;
    errors: string[];
  }> {
    const result = {
      totalProcessed: 0,
      updatedCount: 0,
      endedCount: 0,
      errors: [] as string[],
    };

    try {
      const organizations = await this._getOrganizationsToSync();
      await this._processOrganizations(organizations, result);
    } catch (error) {
      const errorMsg = `Error fetching organizations for sync: ${error.message}`;
      this.logger.error(errorMsg);
      result.errors.push(errorMsg);
    }

    return result;
  }

  private async _getOrganizationsToSync() {
    const organizations =
      await this.organizationService.findOrganizationsWithActiveSubscriptions();
    this.logger.log(
      `Found ${organizations.length} organizations with active subscriptions to sync`,
    );
    return organizations;
  }

  private async _processOrganizations(organizations, result) {
    for (const org of organizations) {
      result.totalProcessed++;
      try {
        await this._syncOrganizationSubscription(org, result);
      } catch (orgError) {
        const errorMsg = `Error syncing organization ${org._id}: ${orgError.message}`;
        this.logger.error(errorMsg);
        result.errors.push(errorMsg);
      }
    }
  }

  private async _syncOrganizationSubscription(org, result) {
    if (!org.stripeCustomerId) {
      this.logger.warn(
        `Organization ${org._id} has active subscription status but no stripeCustomerId`,
      );
      return;
    }

    const stripeSubscriptions = await this.stripeService
      .getStripeInstance()
      .subscriptions.list({
        customer: org.stripeCustomerId,
        limit: 10,
      });

    const activeSubscription = stripeSubscriptions.data.find(
      (sub) =>
        sub.status === 'active' ||
        sub.status === 'trialing' ||
        sub.status === 'past_due',
    );

    if (!activeSubscription) {
      await this._handleNoActiveStripeSubscription(org, result);
    } else {
      await this._updateOrganizationSubscription(
        org,
        activeSubscription,
        result,
      );
    }
  }

  private async _handleNoActiveStripeSubscription(org, result) {
    this.logger.log(
      `No active subscription found in Stripe for organization ${org._id}, resetting to free tier`,
    );
    await this.organizationService.updateOrganization(org._id, {
      planStatus: 'free',
      planTier: StripePlanMapping.FREE,
      currentPeriodEnds: new Date(),
      $unset: {
        stripeSubscriptionId: 1,
        planId: 1,
      },
    });
    result.endedCount++;
  }

  private async _updateOrganizationSubscription(
    org,
    activeSubscription,
    result,
  ) {
    const needsUpdate =
      org.stripeSubscriptionId !== activeSubscription.id ||
      org.planStatus !== activeSubscription.status ||
      org.planId !== activeSubscription.items.data[0]?.price.id ||
      this.isCurrentPeriodEndsDifferent(
        org.currentPeriodEnds,
        activeSubscription.items.data[0]?.current_period_end,
      );

    if (needsUpdate) {
      this.logger.log(
        `Updating organization ${org._id} subscription data from Stripe`,
      );
      const planTier = await this.mapStripePriceToPlanTier(
        activeSubscription.items.data[0]?.price,
      );
      await this.organizationService.updateOrganization(org._id, {
        stripeSubscriptionId: activeSubscription.id,
        planStatus: activeSubscription.status as StripePlanStatus,
        planId: activeSubscription.items.data[0]?.price.id,
        planTier,
        currentPeriodEnds: activeSubscription.items.data[0]?.current_period_end
          ? new Date(activeSubscription.items.data[0].current_period_end * 1000)
          : null,
      });
      result.updatedCount++;
    }
  }
  '';

  /**
   * Helper method to check if currentPeriodEnds differs from Stripe timestamp
   */
  private isCurrentPeriodEndsDifferent(
    localDate: Date | null,
    stripeTimestamp: number,
  ): boolean {
    if (!localDate && !stripeTimestamp) return false;
    if (!localDate || !stripeTimestamp) return true;

    const stripeDate = new Date(stripeTimestamp * 1000);
    return Math.abs(localDate.getTime() - stripeDate.getTime()) > 1000; // Allow 1 second difference
  }

  /**
   * Maps Stripe price to plan tier (reused from webhook service logic)
   */
  private async mapStripePriceToPlanTier(
    price: any,
  ): Promise<StripePlanMapping> {
    if (!price || !price.product) {
      this.logger.warn(
        'mapStripePriceToPlanTier: Price or price.product is undefined, defaulting to FREE.',
      );
      return StripePlanMapping.FREE;
    }

    try {
      let product: any;
      if (typeof price.product === 'string') {
        product = await this.stripeService
          .getStripeInstance()
          .products.retrieve(price.product);
      } else {
        product = price.product;
      }

      if (product && product.metadata && product.metadata.id) {
        const tierMetadata = product.metadata.id.toLowerCase();
        switch (tierMetadata) {
          case 'pro':
            return StripePlanMapping.PRO;
          case 'business':
            return StripePlanMapping.BUSINESS;
          case 'enterprise':
            return StripePlanMapping.ENTERPRISE;
          case 'free':
            return StripePlanMapping.FREE;
          default:
            this.logger.warn(
              `Unknown tier '${tierMetadata}' in product metadata, defaulting to FREE.`,
            );
            return StripePlanMapping.FREE;
        }
      } else {
        this.logger.warn(
          `Product metadata missing for price ${price.id}, defaulting to FREE.`,
        );
        return StripePlanMapping.FREE;
      }
    } catch (error) {
      this.logger.error(`Error mapping price to plan tier: ${error.message}`);
      return StripePlanMapping.FREE;
    }
  }
}
