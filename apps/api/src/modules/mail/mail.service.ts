import { Injectable, Logger } from '@nestjs/common';
import {
  MailDataRequired,
  MailService as SendGridMailService,
} from '@sendgrid/mail';
import { SignUpDto } from 'shared/dto/auth/sign-up.dto';
import { EmailTemplate } from 'shared/types';

const APP_NAME = 'CV Builder';

const sgMail = new SendGridMailService();
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);

  private async sendEmail(msg: MailDataRequired) {
    if (process.env.MOCK_EMAILS || false) {
      this.logger.debug('Development mode - Email details:', {
        to: msg.to,
        from: msg.from,
        templateId: msg.templateId,
        dynamicTemplateData: msg.dynamicTemplateData,
      });
    } else {
      return sgMail.send(msg);
    }
  }

  async sendInviteEmail(
    email: string,
    token: string,
    organizationName: string,
  ) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL,
        templateId: EmailTemplate.INVITATION_SENT,
        dynamicTemplateData: {
          organizationName,
          appName: APP_NAME,
          inviteLink: `${process.env.VITE_WEB_BASE_URL}/accept-invite?token=${token}`,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Invite email sent successfully to ${email} for organization ${organizationName}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send invite email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendSignUpConfirmation(dto: SignUpDto, verificationToken: string) {
    try {
      const { email } = dto;

      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL,
        templateId: EmailTemplate.SIGNUP_CONFIRMATION,
        dynamicTemplateData: {
          first_name: email,
          verificationLink: `${process.env.VITE_API_BASE_URL}/activate/${verificationToken}`,
          appName: APP_NAME,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(
        `Signup confirmation email sent successfully to ${email}`,
      );
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send signup confirmation email to ${dto.email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }

  async sendPasswordResetEmail(email: string, resetToken: string) {
    try {
      const msg = {
        to: email,
        from: process.env.SENDGRID_FROM_EMAIL,
        templateId: EmailTemplate.RESET_PASSWORD,
        dynamicTemplateData: {
          first_name: email,
          resetLink: `${
            process.env.VITE_WEB_BASE_URL
          }${'/set-new-password'}?token=${resetToken}`,
          appName: APP_NAME,
        },
      };

      const info = await this.sendEmail(msg);
      this.logger.log(`Password reset email sent successfully to ${email}`);
      return info;
    } catch (error: unknown) {
      this.logger.error(
        `Failed to send password reset email to ${email}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
      throw error;
    }
  }
}
