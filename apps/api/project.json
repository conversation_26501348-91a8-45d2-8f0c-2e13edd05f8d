{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": [], "targets": {"serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "api:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "api:build:development", "inspect": true}, "production": {"buildTarget": "api:build:production"}}}}}