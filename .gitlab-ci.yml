# Global variables
variables:
  GIT_DEPTH: 0
  NODE_ENV: production
  DOCKER_IMAGE_NAME: '$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG'
  SSM_ENV_PARAMETER: '/cvinventory/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/env'
  SSM_DOCKER_SCRIPT_PARAMETER: '/cvinventory/$CI_PROJECT_NAME/$CI_COMMIT_REF_SLUG/setup_docker'
  YARN_REGISTRY: 'https://registry.npmjs.org'
  # Cache optimization variables
  FF_USE_FASTZIP: "true"
  ARTIFACT_COMPRESSION_LEVEL: "fastest"
  CACHE_COMPRESSION_LEVEL: "fastest"

stages:
  - validate
  - build
  - deploy

# ----------------------------------------------------------------------------
# Cache Templates & Anchors
# ----------------------------------------------------------------------------

.cache_template: &cache_template
  cache:
    key:
      files:
        - yarn.lock
        - package.json
      prefix: ${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .yarn/cache/
      - .nx/cache/
    policy: pull-push
    when: always
    fallback_keys:
      - cache-${CI_DEFAULT_BRANCH}
      - cache-default

.node_setup: &node_setup
  image: node:22-alpine
  <<: *cache_template
  before_script:
    - yarn config set npmRegistryServer $YARN_REGISTRY
    - echo "Node $(node -v)"
    - echo "Yarn $(yarn -v)"
    # Smart cache validation - only install if needed
    - |
      if [ ! -d node_modules/.bin ] || ! yarn check --verify-tree; then
        echo "Installing dependencies..."
        yarn install --frozen-lockfile --ignore-scripts --prefer-offline --network-timeout 100000
      else
        echo "Dependencies are up-to-date, skipping install"
      fi

# ----------------------------------------------------------------------------
# Job Templates
# ----------------------------------------------------------------------------

.validate_job: &validate_job
  stage: validate
  <<: *node_setup
  variables:
    NODE_ENV: development
  rules:
    - if: $CI_COMMIT_REF_NAME != "dev" && $CI_COMMIT_REF_NAME != "main"
  script:
    # Run lint and build in parallel using Nx
    - yarn nx run-many --target=lint --all --parallel --exclude=api-e2e
    - yarn nx run-many --target=build --all --parallel --exclude=api-e2e
  artifacts:
    paths:
      - dist/
    expire_in: 1 week
    when: on_success
  retry: 1
  interruptible: true

.docker_build_job: &docker_build_job
  stage: build
  image: docker:latest
  services:
    - docker:dind
  environment: $CI_COMMIT_REF_NAME
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - echo "Building Docker image $DOCKER_IMAGE_NAME"
    - echo "Using VITE_API_BASE_URL=$VITE_API_BASE_URL"
    - echo "Using VITE_WEB_BASE_URL=$VITE_WEB_BASE_URL"
    # Use Docker layer caching for faster builds
    - docker pull $CI_REGISTRY_IMAGE:latest || true
    # Generate cache key and build Docker image with proper cache invalidation
    - |-
      PACKAGE_CACHE_KEY=$(sha256sum package.json yarn.lock | sha256sum | cut -d' ' -f1)
      echo "Package cache key: $PACKAGE_CACHE_KEY"
      docker build --cache-from $CI_REGISTRY_IMAGE:latest \
        --build-arg VITE_API_BASE_URL="$VITE_API_BASE_URL" \
        --build-arg VITE_WEB_BASE_URL="$VITE_WEB_BASE_URL" \
        --build-arg CACHE_BUST="$PACKAGE_CACHE_KEY" \
        --tag $DOCKER_IMAGE_NAME \
        --tag $CI_REGISTRY_IMAGE:latest .
    - docker push $DOCKER_IMAGE_NAME
    - docker push $CI_REGISTRY_IMAGE:latest
    - echo "Docker image pushed successfully"
  retry: 1

.dev_deploy_job: &dev_deploy_job
  stage: deploy
  image: python:3.12-alpine
  environment: $CI_COMMIT_REF_NAME
  before_script:
    - apk add --no-cache git aws-cli
    - aws --version
    - echo "AWS Region is $AWS_REGION"
    - echo "AWS Instance ID is $AWS_INSTANCE_ID"
  script:
    - echo "Configuring AWS CLI"
    - aws configure set aws_access_key_id "$AWS_ACCESS_KEY_ID"
    - aws configure set aws_secret_access_key "$AWS_SECRET_ACCESS_KEY"
    - aws configure set region "$AWS_REGION"
    - aws configure set output text
    - export AWS_DEFAULT_REGION="$AWS_REGION"
    - echo "Verifying AWS identity"
    - aws sts get-caller-identity
    - echo "Generating .env file"
    - apk add --no-cache bash
    - chmod +x setup_env.sh
    - bash setup_env.sh
    - echo "Uploading .env file to SSM Parameter Store"
    - aws ssm put-parameter --name "$SSM_ENV_PARAMETER" --type "SecureString" --value "$(cat .env)" --overwrite
    - echo "Uploading setup_docker.sh to SSM Parameter Store"
    - aws ssm put-parameter --name "$SSM_DOCKER_SCRIPT_PARAMETER" --type "SecureString" --value "$(cat setup_docker.sh)" --overwrite
    - echo "Running deployment command on EC2"
    - DEPLOY_COMMAND="mkdir -p /opt/deploy && cd /opt/deploy && aws ssm get-parameter --name $SSM_ENV_PARAMETER --with-decryption --query Parameter.Value --output text > .env && aws ssm get-parameter --name $SSM_DOCKER_SCRIPT_PARAMETER --with-decryption --query Parameter.Value --output text > setup_docker.sh && chmod +x setup_docker.sh && ./setup_docker.sh"
    - aws ssm send-command --document-name "AWS-RunShellScript" --targets Key=instanceIds,Values=$AWS_INSTANCE_ID --parameters '{"commands":["'"$DEPLOY_COMMAND"'"]}' --output text
    - echo "Deployment command sent successfully"

# ----------------------------------------------------------------------------
# Actual Jobs
# ----------------------------------------------------------------------------

# Validation job for feature branches
validate:
  extends:
    - .validate_job

# Docker build for dev branch
build_docker_dev:
  extends:
    - .docker_build_job
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev"

# Docker build for main branch
build_docker_main:
  extends:
    - .docker_build_job
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"

# Dev deployment
dev_deploy:
  extends:
    - .dev_deploy_job
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev"
  needs:
    - build_docker_dev

# Main deployment placeholder (ready for production setup)
main_deploy:
  stage: deploy
  image: alpine:latest
  environment: $CI_COMMIT_REF_NAME
  rules:
    - if: $CI_COMMIT_REF_NAME == "main"
  needs:
    - build_docker_main
  script:
    - echo 'Production deployment placeholder'
    - echo 'This will be replaced with actual production deployment steps'
    - echo 'Docker image built successfully'
    - echo 'Ready for production AWS EC2 deployment configuration'
